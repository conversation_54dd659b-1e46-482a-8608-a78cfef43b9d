//
//
// File generated from our OpenAPI spec
//
//

package stripe

import "github.com/stripe/stripe-go/v82/form"

// Place of supply scheme used in an Default standard registration.
type TaxRegistrationCountryOptionsAeStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsAeStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsAeStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsAeStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsAeStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsAeStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsAeType string

// List of values that TaxRegistrationCountryOptionsAeType can take
const (
	TaxRegistrationCountryOptionsAeTypeStandard TaxRegistrationCountryOptionsAeType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsAlType string

// List of values that TaxRegistrationCountryOptionsAlType can take
const (
	TaxRegistrationCountryOptionsAlTypeStandard TaxRegistrationCountryOptionsAlType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsAmType string

// List of values that TaxRegistrationCountryOptionsAmType can take
const (
	TaxRegistrationCountryOptionsAmTypeSimplified TaxRegistrationCountryOptionsAmType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsAoType string

// List of values that TaxRegistrationCountryOptionsAoType can take
const (
	TaxRegistrationCountryOptionsAoTypeStandard TaxRegistrationCountryOptionsAoType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsAtStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsAtStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsAtStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsAtStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsAtStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsAtStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsAtStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsAtStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsAtType string

// List of values that TaxRegistrationCountryOptionsAtType can take
const (
	TaxRegistrationCountryOptionsAtTypeIoss        TaxRegistrationCountryOptionsAtType = "ioss"
	TaxRegistrationCountryOptionsAtTypeOssNonUnion TaxRegistrationCountryOptionsAtType = "oss_non_union"
	TaxRegistrationCountryOptionsAtTypeOssUnion    TaxRegistrationCountryOptionsAtType = "oss_union"
	TaxRegistrationCountryOptionsAtTypeStandard    TaxRegistrationCountryOptionsAtType = "standard"
)

// Place of supply scheme used in an Default standard registration.
type TaxRegistrationCountryOptionsAuStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsAuStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsAuStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsAuStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsAuStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsAuStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsAuType string

// List of values that TaxRegistrationCountryOptionsAuType can take
const (
	TaxRegistrationCountryOptionsAuTypeStandard TaxRegistrationCountryOptionsAuType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsAwType string

// List of values that TaxRegistrationCountryOptionsAwType can take
const (
	TaxRegistrationCountryOptionsAwTypeStandard TaxRegistrationCountryOptionsAwType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsAzType string

// List of values that TaxRegistrationCountryOptionsAzType can take
const (
	TaxRegistrationCountryOptionsAzTypeSimplified TaxRegistrationCountryOptionsAzType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsBaType string

// List of values that TaxRegistrationCountryOptionsBaType can take
const (
	TaxRegistrationCountryOptionsBaTypeStandard TaxRegistrationCountryOptionsBaType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsBbType string

// List of values that TaxRegistrationCountryOptionsBbType can take
const (
	TaxRegistrationCountryOptionsBbTypeStandard TaxRegistrationCountryOptionsBbType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsBdType string

// List of values that TaxRegistrationCountryOptionsBdType can take
const (
	TaxRegistrationCountryOptionsBdTypeStandard TaxRegistrationCountryOptionsBdType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsBeStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsBeStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsBeStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsBeStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsBeStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsBeStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsBeStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsBeStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsBeType string

// List of values that TaxRegistrationCountryOptionsBeType can take
const (
	TaxRegistrationCountryOptionsBeTypeIoss        TaxRegistrationCountryOptionsBeType = "ioss"
	TaxRegistrationCountryOptionsBeTypeOssNonUnion TaxRegistrationCountryOptionsBeType = "oss_non_union"
	TaxRegistrationCountryOptionsBeTypeOssUnion    TaxRegistrationCountryOptionsBeType = "oss_union"
	TaxRegistrationCountryOptionsBeTypeStandard    TaxRegistrationCountryOptionsBeType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsBfType string

// List of values that TaxRegistrationCountryOptionsBfType can take
const (
	TaxRegistrationCountryOptionsBfTypeStandard TaxRegistrationCountryOptionsBfType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsBGStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsBGStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsBGStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsBGStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsBGStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsBGStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsBGStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsBGStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsBGType string

// List of values that TaxRegistrationCountryOptionsBGType can take
const (
	TaxRegistrationCountryOptionsBGTypeIoss        TaxRegistrationCountryOptionsBGType = "ioss"
	TaxRegistrationCountryOptionsBGTypeOssNonUnion TaxRegistrationCountryOptionsBGType = "oss_non_union"
	TaxRegistrationCountryOptionsBGTypeOssUnion    TaxRegistrationCountryOptionsBGType = "oss_union"
	TaxRegistrationCountryOptionsBGTypeStandard    TaxRegistrationCountryOptionsBGType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsBhType string

// List of values that TaxRegistrationCountryOptionsBhType can take
const (
	TaxRegistrationCountryOptionsBhTypeStandard TaxRegistrationCountryOptionsBhType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsBjType string

// List of values that TaxRegistrationCountryOptionsBjType can take
const (
	TaxRegistrationCountryOptionsBjTypeSimplified TaxRegistrationCountryOptionsBjType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsBsType string

// List of values that TaxRegistrationCountryOptionsBsType can take
const (
	TaxRegistrationCountryOptionsBsTypeStandard TaxRegistrationCountryOptionsBsType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsByType string

// List of values that TaxRegistrationCountryOptionsByType can take
const (
	TaxRegistrationCountryOptionsByTypeSimplified TaxRegistrationCountryOptionsByType = "simplified"
)

// Type of registration in Canada.
type TaxRegistrationCountryOptionsCaType string

// List of values that TaxRegistrationCountryOptionsCaType can take
const (
	TaxRegistrationCountryOptionsCaTypeProvinceStandard TaxRegistrationCountryOptionsCaType = "province_standard"
	TaxRegistrationCountryOptionsCaTypeSimplified       TaxRegistrationCountryOptionsCaType = "simplified"
	TaxRegistrationCountryOptionsCaTypeStandard         TaxRegistrationCountryOptionsCaType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsCdType string

// List of values that TaxRegistrationCountryOptionsCdType can take
const (
	TaxRegistrationCountryOptionsCdTypeStandard TaxRegistrationCountryOptionsCdType = "standard"
)

// Place of supply scheme used in an Default standard registration.
type TaxRegistrationCountryOptionsChStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsChStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsChStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsChStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsChStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsChStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsChType string

// List of values that TaxRegistrationCountryOptionsChType can take
const (
	TaxRegistrationCountryOptionsChTypeStandard TaxRegistrationCountryOptionsChType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsClType string

// List of values that TaxRegistrationCountryOptionsClType can take
const (
	TaxRegistrationCountryOptionsClTypeSimplified TaxRegistrationCountryOptionsClType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsCmType string

// List of values that TaxRegistrationCountryOptionsCmType can take
const (
	TaxRegistrationCountryOptionsCmTypeSimplified TaxRegistrationCountryOptionsCmType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsCoType string

// List of values that TaxRegistrationCountryOptionsCoType can take
const (
	TaxRegistrationCountryOptionsCoTypeSimplified TaxRegistrationCountryOptionsCoType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsCrType string

// List of values that TaxRegistrationCountryOptionsCrType can take
const (
	TaxRegistrationCountryOptionsCrTypeSimplified TaxRegistrationCountryOptionsCrType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsCvType string

// List of values that TaxRegistrationCountryOptionsCvType can take
const (
	TaxRegistrationCountryOptionsCvTypeSimplified TaxRegistrationCountryOptionsCvType = "simplified"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsCyStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsCyStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsCyStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsCyStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsCyStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsCyStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsCyStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsCyStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsCyType string

// List of values that TaxRegistrationCountryOptionsCyType can take
const (
	TaxRegistrationCountryOptionsCyTypeIoss        TaxRegistrationCountryOptionsCyType = "ioss"
	TaxRegistrationCountryOptionsCyTypeOssNonUnion TaxRegistrationCountryOptionsCyType = "oss_non_union"
	TaxRegistrationCountryOptionsCyTypeOssUnion    TaxRegistrationCountryOptionsCyType = "oss_union"
	TaxRegistrationCountryOptionsCyTypeStandard    TaxRegistrationCountryOptionsCyType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsCzStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsCzStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsCzStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsCzStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsCzStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsCzStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsCzStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsCzStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsCzType string

// List of values that TaxRegistrationCountryOptionsCzType can take
const (
	TaxRegistrationCountryOptionsCzTypeIoss        TaxRegistrationCountryOptionsCzType = "ioss"
	TaxRegistrationCountryOptionsCzTypeOssNonUnion TaxRegistrationCountryOptionsCzType = "oss_non_union"
	TaxRegistrationCountryOptionsCzTypeOssUnion    TaxRegistrationCountryOptionsCzType = "oss_union"
	TaxRegistrationCountryOptionsCzTypeStandard    TaxRegistrationCountryOptionsCzType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsDEStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsDEStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsDEStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsDEStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsDEStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsDEStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsDEStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsDEStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsDEType string

// List of values that TaxRegistrationCountryOptionsDEType can take
const (
	TaxRegistrationCountryOptionsDETypeIoss        TaxRegistrationCountryOptionsDEType = "ioss"
	TaxRegistrationCountryOptionsDETypeOssNonUnion TaxRegistrationCountryOptionsDEType = "oss_non_union"
	TaxRegistrationCountryOptionsDETypeOssUnion    TaxRegistrationCountryOptionsDEType = "oss_union"
	TaxRegistrationCountryOptionsDETypeStandard    TaxRegistrationCountryOptionsDEType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsDkStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsDkStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsDkStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsDkStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsDkStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsDkStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsDkStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsDkStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsDkType string

// List of values that TaxRegistrationCountryOptionsDkType can take
const (
	TaxRegistrationCountryOptionsDkTypeIoss        TaxRegistrationCountryOptionsDkType = "ioss"
	TaxRegistrationCountryOptionsDkTypeOssNonUnion TaxRegistrationCountryOptionsDkType = "oss_non_union"
	TaxRegistrationCountryOptionsDkTypeOssUnion    TaxRegistrationCountryOptionsDkType = "oss_union"
	TaxRegistrationCountryOptionsDkTypeStandard    TaxRegistrationCountryOptionsDkType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsEcType string

// List of values that TaxRegistrationCountryOptionsEcType can take
const (
	TaxRegistrationCountryOptionsEcTypeSimplified TaxRegistrationCountryOptionsEcType = "simplified"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsEeStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsEeStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsEeStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsEeStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsEeStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsEeStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsEeStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsEeStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsEeType string

// List of values that TaxRegistrationCountryOptionsEeType can take
const (
	TaxRegistrationCountryOptionsEeTypeIoss        TaxRegistrationCountryOptionsEeType = "ioss"
	TaxRegistrationCountryOptionsEeTypeOssNonUnion TaxRegistrationCountryOptionsEeType = "oss_non_union"
	TaxRegistrationCountryOptionsEeTypeOssUnion    TaxRegistrationCountryOptionsEeType = "oss_union"
	TaxRegistrationCountryOptionsEeTypeStandard    TaxRegistrationCountryOptionsEeType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsEgType string

// List of values that TaxRegistrationCountryOptionsEgType can take
const (
	TaxRegistrationCountryOptionsEgTypeSimplified TaxRegistrationCountryOptionsEgType = "simplified"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsESStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsESStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsESStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsESStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsESStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsESStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsESStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsESStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsESType string

// List of values that TaxRegistrationCountryOptionsESType can take
const (
	TaxRegistrationCountryOptionsESTypeIoss        TaxRegistrationCountryOptionsESType = "ioss"
	TaxRegistrationCountryOptionsESTypeOssNonUnion TaxRegistrationCountryOptionsESType = "oss_non_union"
	TaxRegistrationCountryOptionsESTypeOssUnion    TaxRegistrationCountryOptionsESType = "oss_union"
	TaxRegistrationCountryOptionsESTypeStandard    TaxRegistrationCountryOptionsESType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsETType string

// List of values that TaxRegistrationCountryOptionsETType can take
const (
	TaxRegistrationCountryOptionsETTypeStandard TaxRegistrationCountryOptionsETType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsFIStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsFIStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsFIStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsFIStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsFIStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsFIStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsFIStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsFIStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsFIType string

// List of values that TaxRegistrationCountryOptionsFIType can take
const (
	TaxRegistrationCountryOptionsFITypeIoss        TaxRegistrationCountryOptionsFIType = "ioss"
	TaxRegistrationCountryOptionsFITypeOssNonUnion TaxRegistrationCountryOptionsFIType = "oss_non_union"
	TaxRegistrationCountryOptionsFITypeOssUnion    TaxRegistrationCountryOptionsFIType = "oss_union"
	TaxRegistrationCountryOptionsFITypeStandard    TaxRegistrationCountryOptionsFIType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsFRStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsFRStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsFRStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsFRStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsFRStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsFRStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsFRStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsFRStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsFRType string

// List of values that TaxRegistrationCountryOptionsFRType can take
const (
	TaxRegistrationCountryOptionsFRTypeIoss        TaxRegistrationCountryOptionsFRType = "ioss"
	TaxRegistrationCountryOptionsFRTypeOssNonUnion TaxRegistrationCountryOptionsFRType = "oss_non_union"
	TaxRegistrationCountryOptionsFRTypeOssUnion    TaxRegistrationCountryOptionsFRType = "oss_union"
	TaxRegistrationCountryOptionsFRTypeStandard    TaxRegistrationCountryOptionsFRType = "standard"
)

// Place of supply scheme used in an Default standard registration.
type TaxRegistrationCountryOptionsGBStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsGBStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsGBStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsGBStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsGBStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsGBStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsGBType string

// List of values that TaxRegistrationCountryOptionsGBType can take
const (
	TaxRegistrationCountryOptionsGBTypeStandard TaxRegistrationCountryOptionsGBType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsGeType string

// List of values that TaxRegistrationCountryOptionsGeType can take
const (
	TaxRegistrationCountryOptionsGeTypeSimplified TaxRegistrationCountryOptionsGeType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsGnType string

// List of values that TaxRegistrationCountryOptionsGnType can take
const (
	TaxRegistrationCountryOptionsGnTypeStandard TaxRegistrationCountryOptionsGnType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsGrStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsGrStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsGrStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsGrStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsGrStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsGrStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsGrStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsGrStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsGrType string

// List of values that TaxRegistrationCountryOptionsGrType can take
const (
	TaxRegistrationCountryOptionsGrTypeIoss        TaxRegistrationCountryOptionsGrType = "ioss"
	TaxRegistrationCountryOptionsGrTypeOssNonUnion TaxRegistrationCountryOptionsGrType = "oss_non_union"
	TaxRegistrationCountryOptionsGrTypeOssUnion    TaxRegistrationCountryOptionsGrType = "oss_union"
	TaxRegistrationCountryOptionsGrTypeStandard    TaxRegistrationCountryOptionsGrType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsHRStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsHRStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsHRStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsHRStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsHRStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsHRStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsHRStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsHRStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsHRType string

// List of values that TaxRegistrationCountryOptionsHRType can take
const (
	TaxRegistrationCountryOptionsHRTypeIoss        TaxRegistrationCountryOptionsHRType = "ioss"
	TaxRegistrationCountryOptionsHRTypeOssNonUnion TaxRegistrationCountryOptionsHRType = "oss_non_union"
	TaxRegistrationCountryOptionsHRTypeOssUnion    TaxRegistrationCountryOptionsHRType = "oss_union"
	TaxRegistrationCountryOptionsHRTypeStandard    TaxRegistrationCountryOptionsHRType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsHUStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsHUStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsHUStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsHUStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsHUStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsHUStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsHUStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsHUStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsHUType string

// List of values that TaxRegistrationCountryOptionsHUType can take
const (
	TaxRegistrationCountryOptionsHUTypeIoss        TaxRegistrationCountryOptionsHUType = "ioss"
	TaxRegistrationCountryOptionsHUTypeOssNonUnion TaxRegistrationCountryOptionsHUType = "oss_non_union"
	TaxRegistrationCountryOptionsHUTypeOssUnion    TaxRegistrationCountryOptionsHUType = "oss_union"
	TaxRegistrationCountryOptionsHUTypeStandard    TaxRegistrationCountryOptionsHUType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsIDType string

// List of values that TaxRegistrationCountryOptionsIDType can take
const (
	TaxRegistrationCountryOptionsIDTypeSimplified TaxRegistrationCountryOptionsIDType = "simplified"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsIeStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsIeStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsIeStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsIeStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsIeStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsIeStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsIeStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsIeStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsIeType string

// List of values that TaxRegistrationCountryOptionsIeType can take
const (
	TaxRegistrationCountryOptionsIeTypeIoss        TaxRegistrationCountryOptionsIeType = "ioss"
	TaxRegistrationCountryOptionsIeTypeOssNonUnion TaxRegistrationCountryOptionsIeType = "oss_non_union"
	TaxRegistrationCountryOptionsIeTypeOssUnion    TaxRegistrationCountryOptionsIeType = "oss_union"
	TaxRegistrationCountryOptionsIeTypeStandard    TaxRegistrationCountryOptionsIeType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsInType string

// List of values that TaxRegistrationCountryOptionsInType can take
const (
	TaxRegistrationCountryOptionsInTypeSimplified TaxRegistrationCountryOptionsInType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsIsType string

// List of values that TaxRegistrationCountryOptionsIsType can take
const (
	TaxRegistrationCountryOptionsIsTypeStandard TaxRegistrationCountryOptionsIsType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsITStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsITStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsITStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsITStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsITStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsITStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsITStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsITStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsITType string

// List of values that TaxRegistrationCountryOptionsITType can take
const (
	TaxRegistrationCountryOptionsITTypeIoss        TaxRegistrationCountryOptionsITType = "ioss"
	TaxRegistrationCountryOptionsITTypeOssNonUnion TaxRegistrationCountryOptionsITType = "oss_non_union"
	TaxRegistrationCountryOptionsITTypeOssUnion    TaxRegistrationCountryOptionsITType = "oss_union"
	TaxRegistrationCountryOptionsITTypeStandard    TaxRegistrationCountryOptionsITType = "standard"
)

// Place of supply scheme used in an Default standard registration.
type TaxRegistrationCountryOptionsJPStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsJPStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsJPStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsJPStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsJPStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsJPStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsJPType string

// List of values that TaxRegistrationCountryOptionsJPType can take
const (
	TaxRegistrationCountryOptionsJPTypeStandard TaxRegistrationCountryOptionsJPType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsKeType string

// List of values that TaxRegistrationCountryOptionsKeType can take
const (
	TaxRegistrationCountryOptionsKeTypeSimplified TaxRegistrationCountryOptionsKeType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsKgType string

// List of values that TaxRegistrationCountryOptionsKgType can take
const (
	TaxRegistrationCountryOptionsKgTypeSimplified TaxRegistrationCountryOptionsKgType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsKhType string

// List of values that TaxRegistrationCountryOptionsKhType can take
const (
	TaxRegistrationCountryOptionsKhTypeSimplified TaxRegistrationCountryOptionsKhType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsKrType string

// List of values that TaxRegistrationCountryOptionsKrType can take
const (
	TaxRegistrationCountryOptionsKrTypeSimplified TaxRegistrationCountryOptionsKrType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsKzType string

// List of values that TaxRegistrationCountryOptionsKzType can take
const (
	TaxRegistrationCountryOptionsKzTypeSimplified TaxRegistrationCountryOptionsKzType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsLaType string

// List of values that TaxRegistrationCountryOptionsLaType can take
const (
	TaxRegistrationCountryOptionsLaTypeSimplified TaxRegistrationCountryOptionsLaType = "simplified"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsLTStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsLTStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsLTStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsLTStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsLTStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsLTStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsLTStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsLTStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsLTType string

// List of values that TaxRegistrationCountryOptionsLTType can take
const (
	TaxRegistrationCountryOptionsLTTypeIoss        TaxRegistrationCountryOptionsLTType = "ioss"
	TaxRegistrationCountryOptionsLTTypeOssNonUnion TaxRegistrationCountryOptionsLTType = "oss_non_union"
	TaxRegistrationCountryOptionsLTTypeOssUnion    TaxRegistrationCountryOptionsLTType = "oss_union"
	TaxRegistrationCountryOptionsLTTypeStandard    TaxRegistrationCountryOptionsLTType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsLuStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsLuStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsLuStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsLuStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsLuStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsLuStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsLuStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsLuStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsLuType string

// List of values that TaxRegistrationCountryOptionsLuType can take
const (
	TaxRegistrationCountryOptionsLuTypeIoss        TaxRegistrationCountryOptionsLuType = "ioss"
	TaxRegistrationCountryOptionsLuTypeOssNonUnion TaxRegistrationCountryOptionsLuType = "oss_non_union"
	TaxRegistrationCountryOptionsLuTypeOssUnion    TaxRegistrationCountryOptionsLuType = "oss_union"
	TaxRegistrationCountryOptionsLuTypeStandard    TaxRegistrationCountryOptionsLuType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsLVStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsLVStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsLVStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsLVStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsLVStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsLVStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsLVStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsLVStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsLVType string

// List of values that TaxRegistrationCountryOptionsLVType can take
const (
	TaxRegistrationCountryOptionsLVTypeIoss        TaxRegistrationCountryOptionsLVType = "ioss"
	TaxRegistrationCountryOptionsLVTypeOssNonUnion TaxRegistrationCountryOptionsLVType = "oss_non_union"
	TaxRegistrationCountryOptionsLVTypeOssUnion    TaxRegistrationCountryOptionsLVType = "oss_union"
	TaxRegistrationCountryOptionsLVTypeStandard    TaxRegistrationCountryOptionsLVType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsMaType string

// List of values that TaxRegistrationCountryOptionsMaType can take
const (
	TaxRegistrationCountryOptionsMaTypeSimplified TaxRegistrationCountryOptionsMaType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsMdType string

// List of values that TaxRegistrationCountryOptionsMdType can take
const (
	TaxRegistrationCountryOptionsMdTypeSimplified TaxRegistrationCountryOptionsMdType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsMeType string

// List of values that TaxRegistrationCountryOptionsMeType can take
const (
	TaxRegistrationCountryOptionsMeTypeStandard TaxRegistrationCountryOptionsMeType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsMkType string

// List of values that TaxRegistrationCountryOptionsMkType can take
const (
	TaxRegistrationCountryOptionsMkTypeStandard TaxRegistrationCountryOptionsMkType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsMrType string

// List of values that TaxRegistrationCountryOptionsMrType can take
const (
	TaxRegistrationCountryOptionsMrTypeStandard TaxRegistrationCountryOptionsMrType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsMTStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsMTStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsMTStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsMTStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsMTStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsMTStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsMTStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsMTStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsMTType string

// List of values that TaxRegistrationCountryOptionsMTType can take
const (
	TaxRegistrationCountryOptionsMTTypeIoss        TaxRegistrationCountryOptionsMTType = "ioss"
	TaxRegistrationCountryOptionsMTTypeOssNonUnion TaxRegistrationCountryOptionsMTType = "oss_non_union"
	TaxRegistrationCountryOptionsMTTypeOssUnion    TaxRegistrationCountryOptionsMTType = "oss_union"
	TaxRegistrationCountryOptionsMTTypeStandard    TaxRegistrationCountryOptionsMTType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsMXType string

// List of values that TaxRegistrationCountryOptionsMXType can take
const (
	TaxRegistrationCountryOptionsMXTypeSimplified TaxRegistrationCountryOptionsMXType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsMyType string

// List of values that TaxRegistrationCountryOptionsMyType can take
const (
	TaxRegistrationCountryOptionsMyTypeSimplified TaxRegistrationCountryOptionsMyType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsNgType string

// List of values that TaxRegistrationCountryOptionsNgType can take
const (
	TaxRegistrationCountryOptionsNgTypeSimplified TaxRegistrationCountryOptionsNgType = "simplified"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsNLStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsNLStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsNLStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsNLStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsNLStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsNLStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsNLStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsNLStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsNLType string

// List of values that TaxRegistrationCountryOptionsNLType can take
const (
	TaxRegistrationCountryOptionsNLTypeIoss        TaxRegistrationCountryOptionsNLType = "ioss"
	TaxRegistrationCountryOptionsNLTypeOssNonUnion TaxRegistrationCountryOptionsNLType = "oss_non_union"
	TaxRegistrationCountryOptionsNLTypeOssUnion    TaxRegistrationCountryOptionsNLType = "oss_union"
	TaxRegistrationCountryOptionsNLTypeStandard    TaxRegistrationCountryOptionsNLType = "standard"
)

// Place of supply scheme used in an Default standard registration.
type TaxRegistrationCountryOptionsNoStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsNoStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsNoStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsNoStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsNoStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsNoStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsNoType string

// List of values that TaxRegistrationCountryOptionsNoType can take
const (
	TaxRegistrationCountryOptionsNoTypeStandard TaxRegistrationCountryOptionsNoType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsNpType string

// List of values that TaxRegistrationCountryOptionsNpType can take
const (
	TaxRegistrationCountryOptionsNpTypeSimplified TaxRegistrationCountryOptionsNpType = "simplified"
)

// Place of supply scheme used in an Default standard registration.
type TaxRegistrationCountryOptionsNzStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsNzStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsNzStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsNzStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsNzStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsNzStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsNzType string

// List of values that TaxRegistrationCountryOptionsNzType can take
const (
	TaxRegistrationCountryOptionsNzTypeStandard TaxRegistrationCountryOptionsNzType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsOmType string

// List of values that TaxRegistrationCountryOptionsOmType can take
const (
	TaxRegistrationCountryOptionsOmTypeStandard TaxRegistrationCountryOptionsOmType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsPeType string

// List of values that TaxRegistrationCountryOptionsPeType can take
const (
	TaxRegistrationCountryOptionsPeTypeSimplified TaxRegistrationCountryOptionsPeType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsPhType string

// List of values that TaxRegistrationCountryOptionsPhType can take
const (
	TaxRegistrationCountryOptionsPhTypeSimplified TaxRegistrationCountryOptionsPhType = "simplified"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsPLStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsPLStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsPLStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsPLStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsPLStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsPLStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsPLStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsPLStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsPLType string

// List of values that TaxRegistrationCountryOptionsPLType can take
const (
	TaxRegistrationCountryOptionsPLTypeIoss        TaxRegistrationCountryOptionsPLType = "ioss"
	TaxRegistrationCountryOptionsPLTypeOssNonUnion TaxRegistrationCountryOptionsPLType = "oss_non_union"
	TaxRegistrationCountryOptionsPLTypeOssUnion    TaxRegistrationCountryOptionsPLType = "oss_union"
	TaxRegistrationCountryOptionsPLTypeStandard    TaxRegistrationCountryOptionsPLType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsPTStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsPTStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsPTStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsPTStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsPTStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsPTStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsPTStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsPTStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsPTType string

// List of values that TaxRegistrationCountryOptionsPTType can take
const (
	TaxRegistrationCountryOptionsPTTypeIoss        TaxRegistrationCountryOptionsPTType = "ioss"
	TaxRegistrationCountryOptionsPTTypeOssNonUnion TaxRegistrationCountryOptionsPTType = "oss_non_union"
	TaxRegistrationCountryOptionsPTTypeOssUnion    TaxRegistrationCountryOptionsPTType = "oss_union"
	TaxRegistrationCountryOptionsPTTypeStandard    TaxRegistrationCountryOptionsPTType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsROStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsROStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsROStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsROStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsROStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsROStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsROStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsROStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsROType string

// List of values that TaxRegistrationCountryOptionsROType can take
const (
	TaxRegistrationCountryOptionsROTypeIoss        TaxRegistrationCountryOptionsROType = "ioss"
	TaxRegistrationCountryOptionsROTypeOssNonUnion TaxRegistrationCountryOptionsROType = "oss_non_union"
	TaxRegistrationCountryOptionsROTypeOssUnion    TaxRegistrationCountryOptionsROType = "oss_union"
	TaxRegistrationCountryOptionsROTypeStandard    TaxRegistrationCountryOptionsROType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsRsType string

// List of values that TaxRegistrationCountryOptionsRsType can take
const (
	TaxRegistrationCountryOptionsRsTypeStandard TaxRegistrationCountryOptionsRsType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsRUType string

// List of values that TaxRegistrationCountryOptionsRUType can take
const (
	TaxRegistrationCountryOptionsRUTypeSimplified TaxRegistrationCountryOptionsRUType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsSaType string

// List of values that TaxRegistrationCountryOptionsSaType can take
const (
	TaxRegistrationCountryOptionsSaTypeSimplified TaxRegistrationCountryOptionsSaType = "simplified"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsSeStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsSeStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsSeStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsSeStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsSeStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsSeStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsSeStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsSeStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsSeType string

// List of values that TaxRegistrationCountryOptionsSeType can take
const (
	TaxRegistrationCountryOptionsSeTypeIoss        TaxRegistrationCountryOptionsSeType = "ioss"
	TaxRegistrationCountryOptionsSeTypeOssNonUnion TaxRegistrationCountryOptionsSeType = "oss_non_union"
	TaxRegistrationCountryOptionsSeTypeOssUnion    TaxRegistrationCountryOptionsSeType = "oss_union"
	TaxRegistrationCountryOptionsSeTypeStandard    TaxRegistrationCountryOptionsSeType = "standard"
)

// Place of supply scheme used in an Default standard registration.
type TaxRegistrationCountryOptionsSgStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsSgStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsSgStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsSgStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsSgStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsSgStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsSgType string

// List of values that TaxRegistrationCountryOptionsSgType can take
const (
	TaxRegistrationCountryOptionsSgTypeStandard TaxRegistrationCountryOptionsSgType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsSiStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsSiStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsSiStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsSiStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsSiStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsSiStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsSiStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsSiStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsSiType string

// List of values that TaxRegistrationCountryOptionsSiType can take
const (
	TaxRegistrationCountryOptionsSiTypeIoss        TaxRegistrationCountryOptionsSiType = "ioss"
	TaxRegistrationCountryOptionsSiTypeOssNonUnion TaxRegistrationCountryOptionsSiType = "oss_non_union"
	TaxRegistrationCountryOptionsSiTypeOssUnion    TaxRegistrationCountryOptionsSiType = "oss_union"
	TaxRegistrationCountryOptionsSiTypeStandard    TaxRegistrationCountryOptionsSiType = "standard"
)

// Place of supply scheme used in an EU standard registration.
type TaxRegistrationCountryOptionsSKStandardPlaceOfSupplyScheme string

// List of values that TaxRegistrationCountryOptionsSKStandardPlaceOfSupplyScheme can take
const (
	TaxRegistrationCountryOptionsSKStandardPlaceOfSupplySchemeInboundGoods TaxRegistrationCountryOptionsSKStandardPlaceOfSupplyScheme = "inbound_goods"
	TaxRegistrationCountryOptionsSKStandardPlaceOfSupplySchemeSmallSeller  TaxRegistrationCountryOptionsSKStandardPlaceOfSupplyScheme = "small_seller"
	TaxRegistrationCountryOptionsSKStandardPlaceOfSupplySchemeStandard     TaxRegistrationCountryOptionsSKStandardPlaceOfSupplyScheme = "standard"
)

// Type of registration in an EU country.
type TaxRegistrationCountryOptionsSKType string

// List of values that TaxRegistrationCountryOptionsSKType can take
const (
	TaxRegistrationCountryOptionsSKTypeIoss        TaxRegistrationCountryOptionsSKType = "ioss"
	TaxRegistrationCountryOptionsSKTypeOssNonUnion TaxRegistrationCountryOptionsSKType = "oss_non_union"
	TaxRegistrationCountryOptionsSKTypeOssUnion    TaxRegistrationCountryOptionsSKType = "oss_union"
	TaxRegistrationCountryOptionsSKTypeStandard    TaxRegistrationCountryOptionsSKType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsSnType string

// List of values that TaxRegistrationCountryOptionsSnType can take
const (
	TaxRegistrationCountryOptionsSnTypeSimplified TaxRegistrationCountryOptionsSnType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsSrType string

// List of values that TaxRegistrationCountryOptionsSrType can take
const (
	TaxRegistrationCountryOptionsSrTypeStandard TaxRegistrationCountryOptionsSrType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsTHType string

// List of values that TaxRegistrationCountryOptionsTHType can take
const (
	TaxRegistrationCountryOptionsTHTypeSimplified TaxRegistrationCountryOptionsTHType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsTjType string

// List of values that TaxRegistrationCountryOptionsTjType can take
const (
	TaxRegistrationCountryOptionsTjTypeSimplified TaxRegistrationCountryOptionsTjType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsTRType string

// List of values that TaxRegistrationCountryOptionsTRType can take
const (
	TaxRegistrationCountryOptionsTRTypeSimplified TaxRegistrationCountryOptionsTRType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsTzType string

// List of values that TaxRegistrationCountryOptionsTzType can take
const (
	TaxRegistrationCountryOptionsTzTypeSimplified TaxRegistrationCountryOptionsTzType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsUaType string

// List of values that TaxRegistrationCountryOptionsUaType can take
const (
	TaxRegistrationCountryOptionsUaTypeSimplified TaxRegistrationCountryOptionsUaType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsUgType string

// List of values that TaxRegistrationCountryOptionsUgType can take
const (
	TaxRegistrationCountryOptionsUgTypeSimplified TaxRegistrationCountryOptionsUgType = "simplified"
)

// The type of the election for the state sales tax registration.
type TaxRegistrationCountryOptionsUSStateSalesTaxElectionType string

// List of values that TaxRegistrationCountryOptionsUSStateSalesTaxElectionType can take
const (
	TaxRegistrationCountryOptionsUSStateSalesTaxElectionTypeLocalUseTax             TaxRegistrationCountryOptionsUSStateSalesTaxElectionType = "local_use_tax"
	TaxRegistrationCountryOptionsUSStateSalesTaxElectionTypeSimplifiedSellersUseTax TaxRegistrationCountryOptionsUSStateSalesTaxElectionType = "simplified_sellers_use_tax"
	TaxRegistrationCountryOptionsUSStateSalesTaxElectionTypeSingleLocalUseTax       TaxRegistrationCountryOptionsUSStateSalesTaxElectionType = "single_local_use_tax"
)

// Type of registration in the US.
type TaxRegistrationCountryOptionsUSType string

// List of values that TaxRegistrationCountryOptionsUSType can take
const (
	TaxRegistrationCountryOptionsUSTypeLocalAmusementTax      TaxRegistrationCountryOptionsUSType = "local_amusement_tax"
	TaxRegistrationCountryOptionsUSTypeLocalLeaseTax          TaxRegistrationCountryOptionsUSType = "local_lease_tax"
	TaxRegistrationCountryOptionsUSTypeStateCommunicationsTax TaxRegistrationCountryOptionsUSType = "state_communications_tax"
	TaxRegistrationCountryOptionsUSTypeStateRetailDeliveryFee TaxRegistrationCountryOptionsUSType = "state_retail_delivery_fee"
	TaxRegistrationCountryOptionsUSTypeStateSalesTax          TaxRegistrationCountryOptionsUSType = "state_sales_tax"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsUyType string

// List of values that TaxRegistrationCountryOptionsUyType can take
const (
	TaxRegistrationCountryOptionsUyTypeStandard TaxRegistrationCountryOptionsUyType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsUzType string

// List of values that TaxRegistrationCountryOptionsUzType can take
const (
	TaxRegistrationCountryOptionsUzTypeSimplified TaxRegistrationCountryOptionsUzType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsVnType string

// List of values that TaxRegistrationCountryOptionsVnType can take
const (
	TaxRegistrationCountryOptionsVnTypeSimplified TaxRegistrationCountryOptionsVnType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsZaType string

// List of values that TaxRegistrationCountryOptionsZaType can take
const (
	TaxRegistrationCountryOptionsZaTypeStandard TaxRegistrationCountryOptionsZaType = "standard"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsZmType string

// List of values that TaxRegistrationCountryOptionsZmType can take
const (
	TaxRegistrationCountryOptionsZmTypeSimplified TaxRegistrationCountryOptionsZmType = "simplified"
)

// Type of registration in `country`.
type TaxRegistrationCountryOptionsZwType string

// List of values that TaxRegistrationCountryOptionsZwType can take
const (
	TaxRegistrationCountryOptionsZwTypeStandard TaxRegistrationCountryOptionsZwType = "standard"
)

// The status of the registration. This field is present for convenience and can be deduced from `active_from` and `expires_at`.
type TaxRegistrationStatus string

// List of values that TaxRegistrationStatus can take
const (
	TaxRegistrationStatusActive    TaxRegistrationStatus = "active"
	TaxRegistrationStatusExpired   TaxRegistrationStatus = "expired"
	TaxRegistrationStatusScheduled TaxRegistrationStatus = "scheduled"
)

// Returns a list of Tax Registration objects.
type TaxRegistrationListParams struct {
	ListParams `form:"*"`
	// Specifies which fields in the response should be expanded.
	Expand []*string `form:"expand"`
	// The status of the Tax Registration.
	Status *string `form:"status"`
}

// AddExpand appends a new field to expand.
func (p *TaxRegistrationListParams) AddExpand(f string) {
	p.Expand = append(p.Expand, &f)
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsAeStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in AE.
type TaxRegistrationCountryOptionsAeParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsAeStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsAlStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in AL.
type TaxRegistrationCountryOptionsAlParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsAlStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in AM.
type TaxRegistrationCountryOptionsAmParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsAoStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in AO.
type TaxRegistrationCountryOptionsAoParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsAoStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsAtStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in AT.
type TaxRegistrationCountryOptionsAtParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsAtStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsAuStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in AU.
type TaxRegistrationCountryOptionsAuParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsAuStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsAwStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in AW.
type TaxRegistrationCountryOptionsAwParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsAwStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in AZ.
type TaxRegistrationCountryOptionsAzParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsBaStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BA.
type TaxRegistrationCountryOptionsBaParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsBaStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsBbStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BB.
type TaxRegistrationCountryOptionsBbParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsBbStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsBdStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BD.
type TaxRegistrationCountryOptionsBdParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsBdStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsBeStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BE.
type TaxRegistrationCountryOptionsBeParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsBeStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsBfStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BF.
type TaxRegistrationCountryOptionsBfParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsBfStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsBGStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BG.
type TaxRegistrationCountryOptionsBGParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsBGStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsBhStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BH.
type TaxRegistrationCountryOptionsBhParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsBhStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in BJ.
type TaxRegistrationCountryOptionsBjParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsBsStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BS.
type TaxRegistrationCountryOptionsBsParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsBsStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in BY.
type TaxRegistrationCountryOptionsByParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the provincial tax registration.
type TaxRegistrationCountryOptionsCaProvinceStandardParams struct {
	// Two-letter CA province code ([ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)).
	Province *string `form:"province"`
}

// Options for the registration in CA.
type TaxRegistrationCountryOptionsCaParams struct {
	// Options for the provincial tax registration.
	ProvinceStandard *TaxRegistrationCountryOptionsCaProvinceStandardParams `form:"province_standard"`
	// Type of registration to be created in Canada.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsCdStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in CD.
type TaxRegistrationCountryOptionsCdParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsCdStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsChStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in CH.
type TaxRegistrationCountryOptionsChParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsChStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in CL.
type TaxRegistrationCountryOptionsClParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in CM.
type TaxRegistrationCountryOptionsCmParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in CO.
type TaxRegistrationCountryOptionsCoParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in CR.
type TaxRegistrationCountryOptionsCrParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in CV.
type TaxRegistrationCountryOptionsCvParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsCyStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in CY.
type TaxRegistrationCountryOptionsCyParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsCyStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsCzStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in CZ.
type TaxRegistrationCountryOptionsCzParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsCzStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsDEStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in DE.
type TaxRegistrationCountryOptionsDEParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsDEStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsDkStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in DK.
type TaxRegistrationCountryOptionsDkParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsDkStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in EC.
type TaxRegistrationCountryOptionsEcParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsEeStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in EE.
type TaxRegistrationCountryOptionsEeParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsEeStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in EG.
type TaxRegistrationCountryOptionsEgParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsESStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in ES.
type TaxRegistrationCountryOptionsESParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsESStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsETStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in ET.
type TaxRegistrationCountryOptionsETParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsETStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsFIStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in FI.
type TaxRegistrationCountryOptionsFIParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsFIStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsFRStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in FR.
type TaxRegistrationCountryOptionsFRParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsFRStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsGBStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in GB.
type TaxRegistrationCountryOptionsGBParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsGBStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in GE.
type TaxRegistrationCountryOptionsGeParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsGnStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in GN.
type TaxRegistrationCountryOptionsGnParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsGnStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsGrStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in GR.
type TaxRegistrationCountryOptionsGrParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsGrStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsHRStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in HR.
type TaxRegistrationCountryOptionsHRParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsHRStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsHUStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in HU.
type TaxRegistrationCountryOptionsHUParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsHUStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in ID.
type TaxRegistrationCountryOptionsIDParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsIeStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in IE.
type TaxRegistrationCountryOptionsIeParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsIeStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in IN.
type TaxRegistrationCountryOptionsInParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsIsStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in IS.
type TaxRegistrationCountryOptionsIsParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsIsStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsITStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in IT.
type TaxRegistrationCountryOptionsITParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsITStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsJPStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in JP.
type TaxRegistrationCountryOptionsJPParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsJPStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in KE.
type TaxRegistrationCountryOptionsKeParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in KG.
type TaxRegistrationCountryOptionsKgParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in KH.
type TaxRegistrationCountryOptionsKhParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in KR.
type TaxRegistrationCountryOptionsKrParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in KZ.
type TaxRegistrationCountryOptionsKzParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in LA.
type TaxRegistrationCountryOptionsLaParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsLTStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in LT.
type TaxRegistrationCountryOptionsLTParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsLTStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsLuStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in LU.
type TaxRegistrationCountryOptionsLuParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsLuStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsLVStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in LV.
type TaxRegistrationCountryOptionsLVParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsLVStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in MA.
type TaxRegistrationCountryOptionsMaParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in MD.
type TaxRegistrationCountryOptionsMdParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsMeStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in ME.
type TaxRegistrationCountryOptionsMeParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsMeStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsMkStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in MK.
type TaxRegistrationCountryOptionsMkParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsMkStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsMrStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in MR.
type TaxRegistrationCountryOptionsMrParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsMrStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsMTStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in MT.
type TaxRegistrationCountryOptionsMTParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsMTStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in MX.
type TaxRegistrationCountryOptionsMXParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in MY.
type TaxRegistrationCountryOptionsMyParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in NG.
type TaxRegistrationCountryOptionsNgParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsNLStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in NL.
type TaxRegistrationCountryOptionsNLParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsNLStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsNoStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in NO.
type TaxRegistrationCountryOptionsNoParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsNoStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in NP.
type TaxRegistrationCountryOptionsNpParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsNzStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in NZ.
type TaxRegistrationCountryOptionsNzParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsNzStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsOmStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in OM.
type TaxRegistrationCountryOptionsOmParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsOmStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in PE.
type TaxRegistrationCountryOptionsPeParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in PH.
type TaxRegistrationCountryOptionsPhParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsPLStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in PL.
type TaxRegistrationCountryOptionsPLParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsPLStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsPTStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in PT.
type TaxRegistrationCountryOptionsPTParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsPTStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsROStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in RO.
type TaxRegistrationCountryOptionsROParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsROStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsRsStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in RS.
type TaxRegistrationCountryOptionsRsParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsRsStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in RU.
type TaxRegistrationCountryOptionsRUParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in SA.
type TaxRegistrationCountryOptionsSaParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsSeStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in SE.
type TaxRegistrationCountryOptionsSeParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsSeStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsSgStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in SG.
type TaxRegistrationCountryOptionsSgParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsSgStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsSiStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in SI.
type TaxRegistrationCountryOptionsSiParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsSiStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsSKStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in SK.
type TaxRegistrationCountryOptionsSKParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsSKStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in SN.
type TaxRegistrationCountryOptionsSnParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsSrStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in SR.
type TaxRegistrationCountryOptionsSrParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsSrStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in TH.
type TaxRegistrationCountryOptionsTHParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in TJ.
type TaxRegistrationCountryOptionsTjParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in TR.
type TaxRegistrationCountryOptionsTRParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in TZ.
type TaxRegistrationCountryOptionsTzParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in UA.
type TaxRegistrationCountryOptionsUaParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in UG.
type TaxRegistrationCountryOptionsUgParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the local amusement tax registration.
type TaxRegistrationCountryOptionsUSLocalAmusementTaxParams struct {
	// A [FIPS code](https://www.census.gov/library/reference/code-lists/ansi.html) representing the local jurisdiction. Supported FIPS codes are: `14000` (Chicago), `06613` (Bloomington), `21696` (East Dundee), `24582` (Evanston), `45421` (Lynwood), `48892` (Midlothian), `64343` (River Grove), and `68081` (Schiller Park).
	Jurisdiction *string `form:"jurisdiction"`
}

// Options for the local lease tax registration.
type TaxRegistrationCountryOptionsUSLocalLeaseTaxParams struct {
	// A [FIPS code](https://www.census.gov/library/reference/code-lists/ansi.html) representing the local jurisdiction. Supported FIPS codes are: `14000` (Chicago).
	Jurisdiction *string `form:"jurisdiction"`
}

// Elections for the state sales tax registration.
type TaxRegistrationCountryOptionsUSStateSalesTaxElectionParams struct {
	// A [FIPS code](https://www.census.gov/library/reference/code-lists/ansi.html) representing the local jurisdiction. Supported FIPS codes are: `003` (Allegheny County) and `60000` (Philadelphia City).
	Jurisdiction *string `form:"jurisdiction"`
	// The type of the election for the state sales tax registration.
	Type *string `form:"type"`
}

// Options for the state sales tax registration.
type TaxRegistrationCountryOptionsUSStateSalesTaxParams struct {
	// Elections for the state sales tax registration.
	Elections []*TaxRegistrationCountryOptionsUSStateSalesTaxElectionParams `form:"elections"`
}

// Options for the registration in US.
type TaxRegistrationCountryOptionsUSParams struct {
	// Options for the local amusement tax registration.
	LocalAmusementTax *TaxRegistrationCountryOptionsUSLocalAmusementTaxParams `form:"local_amusement_tax"`
	// Options for the local lease tax registration.
	LocalLeaseTax *TaxRegistrationCountryOptionsUSLocalLeaseTaxParams `form:"local_lease_tax"`
	// Two-letter US state code ([ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)).
	State *string `form:"state"`
	// Options for the state sales tax registration.
	StateSalesTax *TaxRegistrationCountryOptionsUSStateSalesTaxParams `form:"state_sales_tax"`
	// Type of registration to be created in the US.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsUyStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in UY.
type TaxRegistrationCountryOptionsUyParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsUyStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in UZ.
type TaxRegistrationCountryOptionsUzParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in VN.
type TaxRegistrationCountryOptionsVnParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsZaStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in ZA.
type TaxRegistrationCountryOptionsZaParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsZaStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in ZM.
type TaxRegistrationCountryOptionsZmParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCountryOptionsZwStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in ZW.
type TaxRegistrationCountryOptionsZwParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCountryOptionsZwStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Specific options for a registration in the specified `country`.
type TaxRegistrationCountryOptionsParams struct {
	// Options for the registration in AE.
	Ae *TaxRegistrationCountryOptionsAeParams `form:"ae"`
	// Options for the registration in AL.
	Al *TaxRegistrationCountryOptionsAlParams `form:"al"`
	// Options for the registration in AM.
	Am *TaxRegistrationCountryOptionsAmParams `form:"am"`
	// Options for the registration in AO.
	Ao *TaxRegistrationCountryOptionsAoParams `form:"ao"`
	// Options for the registration in AT.
	At *TaxRegistrationCountryOptionsAtParams `form:"at"`
	// Options for the registration in AU.
	Au *TaxRegistrationCountryOptionsAuParams `form:"au"`
	// Options for the registration in AW.
	Aw *TaxRegistrationCountryOptionsAwParams `form:"aw"`
	// Options for the registration in AZ.
	Az *TaxRegistrationCountryOptionsAzParams `form:"az"`
	// Options for the registration in BA.
	Ba *TaxRegistrationCountryOptionsBaParams `form:"ba"`
	// Options for the registration in BB.
	Bb *TaxRegistrationCountryOptionsBbParams `form:"bb"`
	// Options for the registration in BD.
	Bd *TaxRegistrationCountryOptionsBdParams `form:"bd"`
	// Options for the registration in BE.
	Be *TaxRegistrationCountryOptionsBeParams `form:"be"`
	// Options for the registration in BF.
	Bf *TaxRegistrationCountryOptionsBfParams `form:"bf"`
	// Options for the registration in BG.
	BG *TaxRegistrationCountryOptionsBGParams `form:"bg"`
	// Options for the registration in BH.
	Bh *TaxRegistrationCountryOptionsBhParams `form:"bh"`
	// Options for the registration in BJ.
	Bj *TaxRegistrationCountryOptionsBjParams `form:"bj"`
	// Options for the registration in BS.
	Bs *TaxRegistrationCountryOptionsBsParams `form:"bs"`
	// Options for the registration in BY.
	By *TaxRegistrationCountryOptionsByParams `form:"by"`
	// Options for the registration in CA.
	Ca *TaxRegistrationCountryOptionsCaParams `form:"ca"`
	// Options for the registration in CD.
	Cd *TaxRegistrationCountryOptionsCdParams `form:"cd"`
	// Options for the registration in CH.
	Ch *TaxRegistrationCountryOptionsChParams `form:"ch"`
	// Options for the registration in CL.
	Cl *TaxRegistrationCountryOptionsClParams `form:"cl"`
	// Options for the registration in CM.
	Cm *TaxRegistrationCountryOptionsCmParams `form:"cm"`
	// Options for the registration in CO.
	Co *TaxRegistrationCountryOptionsCoParams `form:"co"`
	// Options for the registration in CR.
	Cr *TaxRegistrationCountryOptionsCrParams `form:"cr"`
	// Options for the registration in CV.
	Cv *TaxRegistrationCountryOptionsCvParams `form:"cv"`
	// Options for the registration in CY.
	Cy *TaxRegistrationCountryOptionsCyParams `form:"cy"`
	// Options for the registration in CZ.
	Cz *TaxRegistrationCountryOptionsCzParams `form:"cz"`
	// Options for the registration in DE.
	DE *TaxRegistrationCountryOptionsDEParams `form:"de"`
	// Options for the registration in DK.
	Dk *TaxRegistrationCountryOptionsDkParams `form:"dk"`
	// Options for the registration in EC.
	Ec *TaxRegistrationCountryOptionsEcParams `form:"ec"`
	// Options for the registration in EE.
	Ee *TaxRegistrationCountryOptionsEeParams `form:"ee"`
	// Options for the registration in EG.
	Eg *TaxRegistrationCountryOptionsEgParams `form:"eg"`
	// Options for the registration in ES.
	ES *TaxRegistrationCountryOptionsESParams `form:"es"`
	// Options for the registration in ET.
	ET *TaxRegistrationCountryOptionsETParams `form:"et"`
	// Options for the registration in FI.
	FI *TaxRegistrationCountryOptionsFIParams `form:"fi"`
	// Options for the registration in FR.
	FR *TaxRegistrationCountryOptionsFRParams `form:"fr"`
	// Options for the registration in GB.
	GB *TaxRegistrationCountryOptionsGBParams `form:"gb"`
	// Options for the registration in GE.
	Ge *TaxRegistrationCountryOptionsGeParams `form:"ge"`
	// Options for the registration in GN.
	Gn *TaxRegistrationCountryOptionsGnParams `form:"gn"`
	// Options for the registration in GR.
	Gr *TaxRegistrationCountryOptionsGrParams `form:"gr"`
	// Options for the registration in HR.
	HR *TaxRegistrationCountryOptionsHRParams `form:"hr"`
	// Options for the registration in HU.
	HU *TaxRegistrationCountryOptionsHUParams `form:"hu"`
	// Options for the registration in ID.
	ID *TaxRegistrationCountryOptionsIDParams `form:"id"`
	// Options for the registration in IE.
	Ie *TaxRegistrationCountryOptionsIeParams `form:"ie"`
	// Options for the registration in IN.
	In *TaxRegistrationCountryOptionsInParams `form:"in"`
	// Options for the registration in IS.
	Is *TaxRegistrationCountryOptionsIsParams `form:"is"`
	// Options for the registration in IT.
	IT *TaxRegistrationCountryOptionsITParams `form:"it"`
	// Options for the registration in JP.
	JP *TaxRegistrationCountryOptionsJPParams `form:"jp"`
	// Options for the registration in KE.
	Ke *TaxRegistrationCountryOptionsKeParams `form:"ke"`
	// Options for the registration in KG.
	Kg *TaxRegistrationCountryOptionsKgParams `form:"kg"`
	// Options for the registration in KH.
	Kh *TaxRegistrationCountryOptionsKhParams `form:"kh"`
	// Options for the registration in KR.
	Kr *TaxRegistrationCountryOptionsKrParams `form:"kr"`
	// Options for the registration in KZ.
	Kz *TaxRegistrationCountryOptionsKzParams `form:"kz"`
	// Options for the registration in LA.
	La *TaxRegistrationCountryOptionsLaParams `form:"la"`
	// Options for the registration in LT.
	LT *TaxRegistrationCountryOptionsLTParams `form:"lt"`
	// Options for the registration in LU.
	Lu *TaxRegistrationCountryOptionsLuParams `form:"lu"`
	// Options for the registration in LV.
	LV *TaxRegistrationCountryOptionsLVParams `form:"lv"`
	// Options for the registration in MA.
	Ma *TaxRegistrationCountryOptionsMaParams `form:"ma"`
	// Options for the registration in MD.
	Md *TaxRegistrationCountryOptionsMdParams `form:"md"`
	// Options for the registration in ME.
	Me *TaxRegistrationCountryOptionsMeParams `form:"me"`
	// Options for the registration in MK.
	Mk *TaxRegistrationCountryOptionsMkParams `form:"mk"`
	// Options for the registration in MR.
	Mr *TaxRegistrationCountryOptionsMrParams `form:"mr"`
	// Options for the registration in MT.
	MT *TaxRegistrationCountryOptionsMTParams `form:"mt"`
	// Options for the registration in MX.
	MX *TaxRegistrationCountryOptionsMXParams `form:"mx"`
	// Options for the registration in MY.
	My *TaxRegistrationCountryOptionsMyParams `form:"my"`
	// Options for the registration in NG.
	Ng *TaxRegistrationCountryOptionsNgParams `form:"ng"`
	// Options for the registration in NL.
	NL *TaxRegistrationCountryOptionsNLParams `form:"nl"`
	// Options for the registration in NO.
	No *TaxRegistrationCountryOptionsNoParams `form:"no"`
	// Options for the registration in NP.
	Np *TaxRegistrationCountryOptionsNpParams `form:"np"`
	// Options for the registration in NZ.
	Nz *TaxRegistrationCountryOptionsNzParams `form:"nz"`
	// Options for the registration in OM.
	Om *TaxRegistrationCountryOptionsOmParams `form:"om"`
	// Options for the registration in PE.
	Pe *TaxRegistrationCountryOptionsPeParams `form:"pe"`
	// Options for the registration in PH.
	Ph *TaxRegistrationCountryOptionsPhParams `form:"ph"`
	// Options for the registration in PL.
	PL *TaxRegistrationCountryOptionsPLParams `form:"pl"`
	// Options for the registration in PT.
	PT *TaxRegistrationCountryOptionsPTParams `form:"pt"`
	// Options for the registration in RO.
	RO *TaxRegistrationCountryOptionsROParams `form:"ro"`
	// Options for the registration in RS.
	Rs *TaxRegistrationCountryOptionsRsParams `form:"rs"`
	// Options for the registration in RU.
	RU *TaxRegistrationCountryOptionsRUParams `form:"ru"`
	// Options for the registration in SA.
	Sa *TaxRegistrationCountryOptionsSaParams `form:"sa"`
	// Options for the registration in SE.
	Se *TaxRegistrationCountryOptionsSeParams `form:"se"`
	// Options for the registration in SG.
	Sg *TaxRegistrationCountryOptionsSgParams `form:"sg"`
	// Options for the registration in SI.
	Si *TaxRegistrationCountryOptionsSiParams `form:"si"`
	// Options for the registration in SK.
	SK *TaxRegistrationCountryOptionsSKParams `form:"sk"`
	// Options for the registration in SN.
	Sn *TaxRegistrationCountryOptionsSnParams `form:"sn"`
	// Options for the registration in SR.
	Sr *TaxRegistrationCountryOptionsSrParams `form:"sr"`
	// Options for the registration in TH.
	TH *TaxRegistrationCountryOptionsTHParams `form:"th"`
	// Options for the registration in TJ.
	Tj *TaxRegistrationCountryOptionsTjParams `form:"tj"`
	// Options for the registration in TR.
	TR *TaxRegistrationCountryOptionsTRParams `form:"tr"`
	// Options for the registration in TZ.
	Tz *TaxRegistrationCountryOptionsTzParams `form:"tz"`
	// Options for the registration in UA.
	Ua *TaxRegistrationCountryOptionsUaParams `form:"ua"`
	// Options for the registration in UG.
	Ug *TaxRegistrationCountryOptionsUgParams `form:"ug"`
	// Options for the registration in US.
	US *TaxRegistrationCountryOptionsUSParams `form:"us"`
	// Options for the registration in UY.
	Uy *TaxRegistrationCountryOptionsUyParams `form:"uy"`
	// Options for the registration in UZ.
	Uz *TaxRegistrationCountryOptionsUzParams `form:"uz"`
	// Options for the registration in VN.
	Vn *TaxRegistrationCountryOptionsVnParams `form:"vn"`
	// Options for the registration in ZA.
	Za *TaxRegistrationCountryOptionsZaParams `form:"za"`
	// Options for the registration in ZM.
	Zm *TaxRegistrationCountryOptionsZmParams `form:"zm"`
	// Options for the registration in ZW.
	Zw *TaxRegistrationCountryOptionsZwParams `form:"zw"`
}

// Creates a new Tax Registration object.
type TaxRegistrationParams struct {
	Params `form:"*"`
	// Time at which the Tax Registration becomes active. It can be either `now` to indicate the current time, or a future timestamp measured in seconds since the Unix epoch.
	ActiveFrom    *int64 `form:"active_from"`
	ActiveFromNow *bool  `form:"-"` // See custom AppendTo
	// Two-letter country code ([ISO 3166-1 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)).
	Country *string `form:"country"`
	// Specific options for a registration in the specified `country`.
	CountryOptions *TaxRegistrationCountryOptionsParams `form:"country_options"`
	// Specifies which fields in the response should be expanded.
	Expand []*string `form:"expand"`
	// If set, the registration stops being active at this time. If not set, the registration will be active indefinitely. It can be either `now` to indicate the current time, or a timestamp measured in seconds since the Unix epoch.
	ExpiresAt    *int64 `form:"expires_at"`
	ExpiresAtNow *bool  `form:"-"` // See custom AppendTo
}

// AddExpand appends a new field to expand.
func (p *TaxRegistrationParams) AddExpand(f string) {
	p.Expand = append(p.Expand, &f)
}

// AppendTo implements custom encoding logic for TaxRegistrationParams.
func (p *TaxRegistrationParams) AppendTo(body *form.Values, keyParts []string) {
	if BoolValue(p.ActiveFromNow) {
		body.Add(form.FormatKey(append(keyParts, "active_from")), "now")
	}
	if BoolValue(p.ExpiresAtNow) {
		body.Add(form.FormatKey(append(keyParts, "expires_at")), "now")
	}
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsAeStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in AE.
type TaxRegistrationCreateCountryOptionsAeParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsAeStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsAlStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in AL.
type TaxRegistrationCreateCountryOptionsAlParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsAlStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in AM.
type TaxRegistrationCreateCountryOptionsAmParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsAoStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in AO.
type TaxRegistrationCreateCountryOptionsAoParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsAoStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsAtStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in AT.
type TaxRegistrationCreateCountryOptionsAtParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsAtStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsAuStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in AU.
type TaxRegistrationCreateCountryOptionsAuParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsAuStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsAwStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in AW.
type TaxRegistrationCreateCountryOptionsAwParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsAwStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in AZ.
type TaxRegistrationCreateCountryOptionsAzParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsBaStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BA.
type TaxRegistrationCreateCountryOptionsBaParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsBaStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsBbStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BB.
type TaxRegistrationCreateCountryOptionsBbParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsBbStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsBdStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BD.
type TaxRegistrationCreateCountryOptionsBdParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsBdStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsBeStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BE.
type TaxRegistrationCreateCountryOptionsBeParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsBeStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsBfStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BF.
type TaxRegistrationCreateCountryOptionsBfParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsBfStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsBGStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BG.
type TaxRegistrationCreateCountryOptionsBGParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsBGStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsBhStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BH.
type TaxRegistrationCreateCountryOptionsBhParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsBhStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in BJ.
type TaxRegistrationCreateCountryOptionsBjParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsBsStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in BS.
type TaxRegistrationCreateCountryOptionsBsParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsBsStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in BY.
type TaxRegistrationCreateCountryOptionsByParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the provincial tax registration.
type TaxRegistrationCreateCountryOptionsCaProvinceStandardParams struct {
	// Two-letter CA province code ([ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)).
	Province *string `form:"province"`
}

// Options for the registration in CA.
type TaxRegistrationCreateCountryOptionsCaParams struct {
	// Options for the provincial tax registration.
	ProvinceStandard *TaxRegistrationCreateCountryOptionsCaProvinceStandardParams `form:"province_standard"`
	// Type of registration to be created in Canada.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsCdStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in CD.
type TaxRegistrationCreateCountryOptionsCdParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsCdStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsChStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in CH.
type TaxRegistrationCreateCountryOptionsChParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsChStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in CL.
type TaxRegistrationCreateCountryOptionsClParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in CM.
type TaxRegistrationCreateCountryOptionsCmParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in CO.
type TaxRegistrationCreateCountryOptionsCoParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in CR.
type TaxRegistrationCreateCountryOptionsCrParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in CV.
type TaxRegistrationCreateCountryOptionsCvParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsCyStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in CY.
type TaxRegistrationCreateCountryOptionsCyParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsCyStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsCzStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in CZ.
type TaxRegistrationCreateCountryOptionsCzParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsCzStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsDEStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in DE.
type TaxRegistrationCreateCountryOptionsDEParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsDEStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsDkStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in DK.
type TaxRegistrationCreateCountryOptionsDkParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsDkStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in EC.
type TaxRegistrationCreateCountryOptionsEcParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsEeStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in EE.
type TaxRegistrationCreateCountryOptionsEeParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsEeStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in EG.
type TaxRegistrationCreateCountryOptionsEgParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsESStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in ES.
type TaxRegistrationCreateCountryOptionsESParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsESStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsETStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in ET.
type TaxRegistrationCreateCountryOptionsETParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsETStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsFIStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in FI.
type TaxRegistrationCreateCountryOptionsFIParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsFIStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsFRStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in FR.
type TaxRegistrationCreateCountryOptionsFRParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsFRStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsGBStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in GB.
type TaxRegistrationCreateCountryOptionsGBParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsGBStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in GE.
type TaxRegistrationCreateCountryOptionsGeParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsGnStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in GN.
type TaxRegistrationCreateCountryOptionsGnParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsGnStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsGrStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in GR.
type TaxRegistrationCreateCountryOptionsGrParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsGrStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsHRStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in HR.
type TaxRegistrationCreateCountryOptionsHRParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsHRStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsHUStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in HU.
type TaxRegistrationCreateCountryOptionsHUParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsHUStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in ID.
type TaxRegistrationCreateCountryOptionsIDParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsIeStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in IE.
type TaxRegistrationCreateCountryOptionsIeParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsIeStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in IN.
type TaxRegistrationCreateCountryOptionsInParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsIsStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in IS.
type TaxRegistrationCreateCountryOptionsIsParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsIsStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsITStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in IT.
type TaxRegistrationCreateCountryOptionsITParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsITStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsJPStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in JP.
type TaxRegistrationCreateCountryOptionsJPParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsJPStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in KE.
type TaxRegistrationCreateCountryOptionsKeParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in KG.
type TaxRegistrationCreateCountryOptionsKgParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in KH.
type TaxRegistrationCreateCountryOptionsKhParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in KR.
type TaxRegistrationCreateCountryOptionsKrParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in KZ.
type TaxRegistrationCreateCountryOptionsKzParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in LA.
type TaxRegistrationCreateCountryOptionsLaParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsLTStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in LT.
type TaxRegistrationCreateCountryOptionsLTParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsLTStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsLuStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in LU.
type TaxRegistrationCreateCountryOptionsLuParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsLuStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsLVStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in LV.
type TaxRegistrationCreateCountryOptionsLVParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsLVStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in MA.
type TaxRegistrationCreateCountryOptionsMaParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in MD.
type TaxRegistrationCreateCountryOptionsMdParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsMeStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in ME.
type TaxRegistrationCreateCountryOptionsMeParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsMeStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsMkStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in MK.
type TaxRegistrationCreateCountryOptionsMkParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsMkStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsMrStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in MR.
type TaxRegistrationCreateCountryOptionsMrParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsMrStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsMTStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in MT.
type TaxRegistrationCreateCountryOptionsMTParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsMTStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in MX.
type TaxRegistrationCreateCountryOptionsMXParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in MY.
type TaxRegistrationCreateCountryOptionsMyParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in NG.
type TaxRegistrationCreateCountryOptionsNgParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsNLStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in NL.
type TaxRegistrationCreateCountryOptionsNLParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsNLStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsNoStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in NO.
type TaxRegistrationCreateCountryOptionsNoParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsNoStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in NP.
type TaxRegistrationCreateCountryOptionsNpParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsNzStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in NZ.
type TaxRegistrationCreateCountryOptionsNzParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsNzStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsOmStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in OM.
type TaxRegistrationCreateCountryOptionsOmParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsOmStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in PE.
type TaxRegistrationCreateCountryOptionsPeParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in PH.
type TaxRegistrationCreateCountryOptionsPhParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsPLStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in PL.
type TaxRegistrationCreateCountryOptionsPLParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsPLStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsPTStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in PT.
type TaxRegistrationCreateCountryOptionsPTParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsPTStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsROStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in RO.
type TaxRegistrationCreateCountryOptionsROParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsROStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsRsStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in RS.
type TaxRegistrationCreateCountryOptionsRsParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsRsStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in RU.
type TaxRegistrationCreateCountryOptionsRUParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in SA.
type TaxRegistrationCreateCountryOptionsSaParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsSeStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in SE.
type TaxRegistrationCreateCountryOptionsSeParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsSeStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsSgStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in SG.
type TaxRegistrationCreateCountryOptionsSgParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsSgStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsSiStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in SI.
type TaxRegistrationCreateCountryOptionsSiParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsSiStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsSKStandardParams struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in SK.
type TaxRegistrationCreateCountryOptionsSKParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsSKStandardParams `form:"standard"`
	// Type of registration to be created in an EU country.
	Type *string `form:"type"`
}

// Options for the registration in SN.
type TaxRegistrationCreateCountryOptionsSnParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsSrStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in SR.
type TaxRegistrationCreateCountryOptionsSrParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsSrStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in TH.
type TaxRegistrationCreateCountryOptionsTHParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in TJ.
type TaxRegistrationCreateCountryOptionsTjParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in TR.
type TaxRegistrationCreateCountryOptionsTRParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in TZ.
type TaxRegistrationCreateCountryOptionsTzParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in UA.
type TaxRegistrationCreateCountryOptionsUaParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in UG.
type TaxRegistrationCreateCountryOptionsUgParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the local amusement tax registration.
type TaxRegistrationCreateCountryOptionsUSLocalAmusementTaxParams struct {
	// A [FIPS code](https://www.census.gov/library/reference/code-lists/ansi.html) representing the local jurisdiction. Supported FIPS codes are: `14000` (Chicago), `06613` (Bloomington), `21696` (East Dundee), `24582` (Evanston), `45421` (Lynwood), `48892` (Midlothian), `64343` (River Grove), and `68081` (Schiller Park).
	Jurisdiction *string `form:"jurisdiction"`
}

// Options for the local lease tax registration.
type TaxRegistrationCreateCountryOptionsUSLocalLeaseTaxParams struct {
	// A [FIPS code](https://www.census.gov/library/reference/code-lists/ansi.html) representing the local jurisdiction. Supported FIPS codes are: `14000` (Chicago).
	Jurisdiction *string `form:"jurisdiction"`
}

// Elections for the state sales tax registration.
type TaxRegistrationCreateCountryOptionsUSStateSalesTaxElectionParams struct {
	// A [FIPS code](https://www.census.gov/library/reference/code-lists/ansi.html) representing the local jurisdiction. Supported FIPS codes are: `003` (Allegheny County) and `60000` (Philadelphia City).
	Jurisdiction *string `form:"jurisdiction"`
	// The type of the election for the state sales tax registration.
	Type *string `form:"type"`
}

// Options for the state sales tax registration.
type TaxRegistrationCreateCountryOptionsUSStateSalesTaxParams struct {
	// Elections for the state sales tax registration.
	Elections []*TaxRegistrationCreateCountryOptionsUSStateSalesTaxElectionParams `form:"elections"`
}

// Options for the registration in US.
type TaxRegistrationCreateCountryOptionsUSParams struct {
	// Options for the local amusement tax registration.
	LocalAmusementTax *TaxRegistrationCreateCountryOptionsUSLocalAmusementTaxParams `form:"local_amusement_tax"`
	// Options for the local lease tax registration.
	LocalLeaseTax *TaxRegistrationCreateCountryOptionsUSLocalLeaseTaxParams `form:"local_lease_tax"`
	// Two-letter US state code ([ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)).
	State *string `form:"state"`
	// Options for the state sales tax registration.
	StateSalesTax *TaxRegistrationCreateCountryOptionsUSStateSalesTaxParams `form:"state_sales_tax"`
	// Type of registration to be created in the US.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsUyStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in UY.
type TaxRegistrationCreateCountryOptionsUyParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsUyStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in UZ.
type TaxRegistrationCreateCountryOptionsUzParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in VN.
type TaxRegistrationCreateCountryOptionsVnParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsZaStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in ZA.
type TaxRegistrationCreateCountryOptionsZaParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsZaStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the registration in ZM.
type TaxRegistrationCreateCountryOptionsZmParams struct {
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Options for the standard registration.
type TaxRegistrationCreateCountryOptionsZwStandardParams struct {
	// Place of supply scheme used in an standard registration.
	PlaceOfSupplyScheme *string `form:"place_of_supply_scheme"`
}

// Options for the registration in ZW.
type TaxRegistrationCreateCountryOptionsZwParams struct {
	// Options for the standard registration.
	Standard *TaxRegistrationCreateCountryOptionsZwStandardParams `form:"standard"`
	// Type of registration to be created in `country`.
	Type *string `form:"type"`
}

// Specific options for a registration in the specified `country`.
type TaxRegistrationCreateCountryOptionsParams struct {
	// Options for the registration in AE.
	Ae *TaxRegistrationCreateCountryOptionsAeParams `form:"ae"`
	// Options for the registration in AL.
	Al *TaxRegistrationCreateCountryOptionsAlParams `form:"al"`
	// Options for the registration in AM.
	Am *TaxRegistrationCreateCountryOptionsAmParams `form:"am"`
	// Options for the registration in AO.
	Ao *TaxRegistrationCreateCountryOptionsAoParams `form:"ao"`
	// Options for the registration in AT.
	At *TaxRegistrationCreateCountryOptionsAtParams `form:"at"`
	// Options for the registration in AU.
	Au *TaxRegistrationCreateCountryOptionsAuParams `form:"au"`
	// Options for the registration in AW.
	Aw *TaxRegistrationCreateCountryOptionsAwParams `form:"aw"`
	// Options for the registration in AZ.
	Az *TaxRegistrationCreateCountryOptionsAzParams `form:"az"`
	// Options for the registration in BA.
	Ba *TaxRegistrationCreateCountryOptionsBaParams `form:"ba"`
	// Options for the registration in BB.
	Bb *TaxRegistrationCreateCountryOptionsBbParams `form:"bb"`
	// Options for the registration in BD.
	Bd *TaxRegistrationCreateCountryOptionsBdParams `form:"bd"`
	// Options for the registration in BE.
	Be *TaxRegistrationCreateCountryOptionsBeParams `form:"be"`
	// Options for the registration in BF.
	Bf *TaxRegistrationCreateCountryOptionsBfParams `form:"bf"`
	// Options for the registration in BG.
	BG *TaxRegistrationCreateCountryOptionsBGParams `form:"bg"`
	// Options for the registration in BH.
	Bh *TaxRegistrationCreateCountryOptionsBhParams `form:"bh"`
	// Options for the registration in BJ.
	Bj *TaxRegistrationCreateCountryOptionsBjParams `form:"bj"`
	// Options for the registration in BS.
	Bs *TaxRegistrationCreateCountryOptionsBsParams `form:"bs"`
	// Options for the registration in BY.
	By *TaxRegistrationCreateCountryOptionsByParams `form:"by"`
	// Options for the registration in CA.
	Ca *TaxRegistrationCreateCountryOptionsCaParams `form:"ca"`
	// Options for the registration in CD.
	Cd *TaxRegistrationCreateCountryOptionsCdParams `form:"cd"`
	// Options for the registration in CH.
	Ch *TaxRegistrationCreateCountryOptionsChParams `form:"ch"`
	// Options for the registration in CL.
	Cl *TaxRegistrationCreateCountryOptionsClParams `form:"cl"`
	// Options for the registration in CM.
	Cm *TaxRegistrationCreateCountryOptionsCmParams `form:"cm"`
	// Options for the registration in CO.
	Co *TaxRegistrationCreateCountryOptionsCoParams `form:"co"`
	// Options for the registration in CR.
	Cr *TaxRegistrationCreateCountryOptionsCrParams `form:"cr"`
	// Options for the registration in CV.
	Cv *TaxRegistrationCreateCountryOptionsCvParams `form:"cv"`
	// Options for the registration in CY.
	Cy *TaxRegistrationCreateCountryOptionsCyParams `form:"cy"`
	// Options for the registration in CZ.
	Cz *TaxRegistrationCreateCountryOptionsCzParams `form:"cz"`
	// Options for the registration in DE.
	DE *TaxRegistrationCreateCountryOptionsDEParams `form:"de"`
	// Options for the registration in DK.
	Dk *TaxRegistrationCreateCountryOptionsDkParams `form:"dk"`
	// Options for the registration in EC.
	Ec *TaxRegistrationCreateCountryOptionsEcParams `form:"ec"`
	// Options for the registration in EE.
	Ee *TaxRegistrationCreateCountryOptionsEeParams `form:"ee"`
	// Options for the registration in EG.
	Eg *TaxRegistrationCreateCountryOptionsEgParams `form:"eg"`
	// Options for the registration in ES.
	ES *TaxRegistrationCreateCountryOptionsESParams `form:"es"`
	// Options for the registration in ET.
	ET *TaxRegistrationCreateCountryOptionsETParams `form:"et"`
	// Options for the registration in FI.
	FI *TaxRegistrationCreateCountryOptionsFIParams `form:"fi"`
	// Options for the registration in FR.
	FR *TaxRegistrationCreateCountryOptionsFRParams `form:"fr"`
	// Options for the registration in GB.
	GB *TaxRegistrationCreateCountryOptionsGBParams `form:"gb"`
	// Options for the registration in GE.
	Ge *TaxRegistrationCreateCountryOptionsGeParams `form:"ge"`
	// Options for the registration in GN.
	Gn *TaxRegistrationCreateCountryOptionsGnParams `form:"gn"`
	// Options for the registration in GR.
	Gr *TaxRegistrationCreateCountryOptionsGrParams `form:"gr"`
	// Options for the registration in HR.
	HR *TaxRegistrationCreateCountryOptionsHRParams `form:"hr"`
	// Options for the registration in HU.
	HU *TaxRegistrationCreateCountryOptionsHUParams `form:"hu"`
	// Options for the registration in ID.
	ID *TaxRegistrationCreateCountryOptionsIDParams `form:"id"`
	// Options for the registration in IE.
	Ie *TaxRegistrationCreateCountryOptionsIeParams `form:"ie"`
	// Options for the registration in IN.
	In *TaxRegistrationCreateCountryOptionsInParams `form:"in"`
	// Options for the registration in IS.
	Is *TaxRegistrationCreateCountryOptionsIsParams `form:"is"`
	// Options for the registration in IT.
	IT *TaxRegistrationCreateCountryOptionsITParams `form:"it"`
	// Options for the registration in JP.
	JP *TaxRegistrationCreateCountryOptionsJPParams `form:"jp"`
	// Options for the registration in KE.
	Ke *TaxRegistrationCreateCountryOptionsKeParams `form:"ke"`
	// Options for the registration in KG.
	Kg *TaxRegistrationCreateCountryOptionsKgParams `form:"kg"`
	// Options for the registration in KH.
	Kh *TaxRegistrationCreateCountryOptionsKhParams `form:"kh"`
	// Options for the registration in KR.
	Kr *TaxRegistrationCreateCountryOptionsKrParams `form:"kr"`
	// Options for the registration in KZ.
	Kz *TaxRegistrationCreateCountryOptionsKzParams `form:"kz"`
	// Options for the registration in LA.
	La *TaxRegistrationCreateCountryOptionsLaParams `form:"la"`
	// Options for the registration in LT.
	LT *TaxRegistrationCreateCountryOptionsLTParams `form:"lt"`
	// Options for the registration in LU.
	Lu *TaxRegistrationCreateCountryOptionsLuParams `form:"lu"`
	// Options for the registration in LV.
	LV *TaxRegistrationCreateCountryOptionsLVParams `form:"lv"`
	// Options for the registration in MA.
	Ma *TaxRegistrationCreateCountryOptionsMaParams `form:"ma"`
	// Options for the registration in MD.
	Md *TaxRegistrationCreateCountryOptionsMdParams `form:"md"`
	// Options for the registration in ME.
	Me *TaxRegistrationCreateCountryOptionsMeParams `form:"me"`
	// Options for the registration in MK.
	Mk *TaxRegistrationCreateCountryOptionsMkParams `form:"mk"`
	// Options for the registration in MR.
	Mr *TaxRegistrationCreateCountryOptionsMrParams `form:"mr"`
	// Options for the registration in MT.
	MT *TaxRegistrationCreateCountryOptionsMTParams `form:"mt"`
	// Options for the registration in MX.
	MX *TaxRegistrationCreateCountryOptionsMXParams `form:"mx"`
	// Options for the registration in MY.
	My *TaxRegistrationCreateCountryOptionsMyParams `form:"my"`
	// Options for the registration in NG.
	Ng *TaxRegistrationCreateCountryOptionsNgParams `form:"ng"`
	// Options for the registration in NL.
	NL *TaxRegistrationCreateCountryOptionsNLParams `form:"nl"`
	// Options for the registration in NO.
	No *TaxRegistrationCreateCountryOptionsNoParams `form:"no"`
	// Options for the registration in NP.
	Np *TaxRegistrationCreateCountryOptionsNpParams `form:"np"`
	// Options for the registration in NZ.
	Nz *TaxRegistrationCreateCountryOptionsNzParams `form:"nz"`
	// Options for the registration in OM.
	Om *TaxRegistrationCreateCountryOptionsOmParams `form:"om"`
	// Options for the registration in PE.
	Pe *TaxRegistrationCreateCountryOptionsPeParams `form:"pe"`
	// Options for the registration in PH.
	Ph *TaxRegistrationCreateCountryOptionsPhParams `form:"ph"`
	// Options for the registration in PL.
	PL *TaxRegistrationCreateCountryOptionsPLParams `form:"pl"`
	// Options for the registration in PT.
	PT *TaxRegistrationCreateCountryOptionsPTParams `form:"pt"`
	// Options for the registration in RO.
	RO *TaxRegistrationCreateCountryOptionsROParams `form:"ro"`
	// Options for the registration in RS.
	Rs *TaxRegistrationCreateCountryOptionsRsParams `form:"rs"`
	// Options for the registration in RU.
	RU *TaxRegistrationCreateCountryOptionsRUParams `form:"ru"`
	// Options for the registration in SA.
	Sa *TaxRegistrationCreateCountryOptionsSaParams `form:"sa"`
	// Options for the registration in SE.
	Se *TaxRegistrationCreateCountryOptionsSeParams `form:"se"`
	// Options for the registration in SG.
	Sg *TaxRegistrationCreateCountryOptionsSgParams `form:"sg"`
	// Options for the registration in SI.
	Si *TaxRegistrationCreateCountryOptionsSiParams `form:"si"`
	// Options for the registration in SK.
	SK *TaxRegistrationCreateCountryOptionsSKParams `form:"sk"`
	// Options for the registration in SN.
	Sn *TaxRegistrationCreateCountryOptionsSnParams `form:"sn"`
	// Options for the registration in SR.
	Sr *TaxRegistrationCreateCountryOptionsSrParams `form:"sr"`
	// Options for the registration in TH.
	TH *TaxRegistrationCreateCountryOptionsTHParams `form:"th"`
	// Options for the registration in TJ.
	Tj *TaxRegistrationCreateCountryOptionsTjParams `form:"tj"`
	// Options for the registration in TR.
	TR *TaxRegistrationCreateCountryOptionsTRParams `form:"tr"`
	// Options for the registration in TZ.
	Tz *TaxRegistrationCreateCountryOptionsTzParams `form:"tz"`
	// Options for the registration in UA.
	Ua *TaxRegistrationCreateCountryOptionsUaParams `form:"ua"`
	// Options for the registration in UG.
	Ug *TaxRegistrationCreateCountryOptionsUgParams `form:"ug"`
	// Options for the registration in US.
	US *TaxRegistrationCreateCountryOptionsUSParams `form:"us"`
	// Options for the registration in UY.
	Uy *TaxRegistrationCreateCountryOptionsUyParams `form:"uy"`
	// Options for the registration in UZ.
	Uz *TaxRegistrationCreateCountryOptionsUzParams `form:"uz"`
	// Options for the registration in VN.
	Vn *TaxRegistrationCreateCountryOptionsVnParams `form:"vn"`
	// Options for the registration in ZA.
	Za *TaxRegistrationCreateCountryOptionsZaParams `form:"za"`
	// Options for the registration in ZM.
	Zm *TaxRegistrationCreateCountryOptionsZmParams `form:"zm"`
	// Options for the registration in ZW.
	Zw *TaxRegistrationCreateCountryOptionsZwParams `form:"zw"`
}

// Creates a new Tax Registration object.
type TaxRegistrationCreateParams struct {
	Params `form:"*"`
	// Time at which the Tax Registration becomes active. It can be either `now` to indicate the current time, or a future timestamp measured in seconds since the Unix epoch.
	ActiveFrom    *int64 `form:"active_from"`
	ActiveFromNow *bool  `form:"-"` // See custom AppendTo
	// Two-letter country code ([ISO 3166-1 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)).
	Country *string `form:"country"`
	// Specific options for a registration in the specified `country`.
	CountryOptions *TaxRegistrationCreateCountryOptionsParams `form:"country_options"`
	// Specifies which fields in the response should be expanded.
	Expand []*string `form:"expand"`
	// If set, the Tax Registration stops being active at this time. If not set, the Tax Registration will be active indefinitely. Timestamp measured in seconds since the Unix epoch.
	ExpiresAt *int64 `form:"expires_at"`
}

// AddExpand appends a new field to expand.
func (p *TaxRegistrationCreateParams) AddExpand(f string) {
	p.Expand = append(p.Expand, &f)
}

// AppendTo implements custom encoding logic for TaxRegistrationCreateParams.
func (p *TaxRegistrationCreateParams) AppendTo(body *form.Values, keyParts []string) {
	if BoolValue(p.ActiveFromNow) {
		body.Add(form.FormatKey(append(keyParts, "active_from")), "now")
	}
}

// Returns a Tax Registration object.
type TaxRegistrationRetrieveParams struct {
	Params `form:"*"`
	// Specifies which fields in the response should be expanded.
	Expand []*string `form:"expand"`
}

// AddExpand appends a new field to expand.
func (p *TaxRegistrationRetrieveParams) AddExpand(f string) {
	p.Expand = append(p.Expand, &f)
}

// Updates an existing Tax Registration object.
//
// A registration cannot be deleted after it has been created. If you wish to end a registration you may do so by setting expires_at.
type TaxRegistrationUpdateParams struct {
	Params `form:"*"`
	// Time at which the registration becomes active. It can be either `now` to indicate the current time, or a timestamp measured in seconds since the Unix epoch.
	ActiveFrom    *int64 `form:"active_from"`
	ActiveFromNow *bool  `form:"-"` // See custom AppendTo
	// Specifies which fields in the response should be expanded.
	Expand []*string `form:"expand"`
	// If set, the registration stops being active at this time. If not set, the registration will be active indefinitely. It can be either `now` to indicate the current time, or a timestamp measured in seconds since the Unix epoch.
	ExpiresAt    *int64 `form:"expires_at"`
	ExpiresAtNow *bool  `form:"-"` // See custom AppendTo
}

// AddExpand appends a new field to expand.
func (p *TaxRegistrationUpdateParams) AddExpand(f string) {
	p.Expand = append(p.Expand, &f)
}

// AppendTo implements custom encoding logic for TaxRegistrationUpdateParams.
func (p *TaxRegistrationUpdateParams) AppendTo(body *form.Values, keyParts []string) {
	if BoolValue(p.ActiveFromNow) {
		body.Add(form.FormatKey(append(keyParts, "active_from")), "now")
	}
	if BoolValue(p.ExpiresAtNow) {
		body.Add(form.FormatKey(append(keyParts, "expires_at")), "now")
	}
}

type TaxRegistrationCountryOptionsAeStandard struct {
	// Place of supply scheme used in an Default standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsAeStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsAe struct {
	Standard *TaxRegistrationCountryOptionsAeStandard `json:"standard"`
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsAeType `json:"type"`
}
type TaxRegistrationCountryOptionsAl struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsAlType `json:"type"`
}
type TaxRegistrationCountryOptionsAm struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsAmType `json:"type"`
}
type TaxRegistrationCountryOptionsAo struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsAoType `json:"type"`
}
type TaxRegistrationCountryOptionsAtStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsAtStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsAt struct {
	Standard *TaxRegistrationCountryOptionsAtStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsAtType `json:"type"`
}
type TaxRegistrationCountryOptionsAuStandard struct {
	// Place of supply scheme used in an Default standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsAuStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsAu struct {
	Standard *TaxRegistrationCountryOptionsAuStandard `json:"standard"`
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsAuType `json:"type"`
}
type TaxRegistrationCountryOptionsAw struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsAwType `json:"type"`
}
type TaxRegistrationCountryOptionsAz struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsAzType `json:"type"`
}
type TaxRegistrationCountryOptionsBa struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsBaType `json:"type"`
}
type TaxRegistrationCountryOptionsBb struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsBbType `json:"type"`
}
type TaxRegistrationCountryOptionsBd struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsBdType `json:"type"`
}
type TaxRegistrationCountryOptionsBeStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsBeStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsBe struct {
	Standard *TaxRegistrationCountryOptionsBeStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsBeType `json:"type"`
}
type TaxRegistrationCountryOptionsBf struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsBfType `json:"type"`
}
type TaxRegistrationCountryOptionsBGStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsBGStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsBG struct {
	Standard *TaxRegistrationCountryOptionsBGStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsBGType `json:"type"`
}
type TaxRegistrationCountryOptionsBh struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsBhType `json:"type"`
}
type TaxRegistrationCountryOptionsBj struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsBjType `json:"type"`
}
type TaxRegistrationCountryOptionsBs struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsBsType `json:"type"`
}
type TaxRegistrationCountryOptionsBy struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsByType `json:"type"`
}
type TaxRegistrationCountryOptionsCaProvinceStandard struct {
	// Two-letter CA province code ([ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)).
	Province string `json:"province"`
}
type TaxRegistrationCountryOptionsCa struct {
	ProvinceStandard *TaxRegistrationCountryOptionsCaProvinceStandard `json:"province_standard"`
	// Type of registration in Canada.
	Type TaxRegistrationCountryOptionsCaType `json:"type"`
}
type TaxRegistrationCountryOptionsCd struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsCdType `json:"type"`
}
type TaxRegistrationCountryOptionsChStandard struct {
	// Place of supply scheme used in an Default standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsChStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsCh struct {
	Standard *TaxRegistrationCountryOptionsChStandard `json:"standard"`
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsChType `json:"type"`
}
type TaxRegistrationCountryOptionsCl struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsClType `json:"type"`
}
type TaxRegistrationCountryOptionsCm struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsCmType `json:"type"`
}
type TaxRegistrationCountryOptionsCo struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsCoType `json:"type"`
}
type TaxRegistrationCountryOptionsCr struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsCrType `json:"type"`
}
type TaxRegistrationCountryOptionsCv struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsCvType `json:"type"`
}
type TaxRegistrationCountryOptionsCyStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsCyStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsCy struct {
	Standard *TaxRegistrationCountryOptionsCyStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsCyType `json:"type"`
}
type TaxRegistrationCountryOptionsCzStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsCzStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsCz struct {
	Standard *TaxRegistrationCountryOptionsCzStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsCzType `json:"type"`
}
type TaxRegistrationCountryOptionsDEStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsDEStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsDE struct {
	Standard *TaxRegistrationCountryOptionsDEStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsDEType `json:"type"`
}
type TaxRegistrationCountryOptionsDkStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsDkStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsDk struct {
	Standard *TaxRegistrationCountryOptionsDkStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsDkType `json:"type"`
}
type TaxRegistrationCountryOptionsEc struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsEcType `json:"type"`
}
type TaxRegistrationCountryOptionsEeStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsEeStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsEe struct {
	Standard *TaxRegistrationCountryOptionsEeStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsEeType `json:"type"`
}
type TaxRegistrationCountryOptionsEg struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsEgType `json:"type"`
}
type TaxRegistrationCountryOptionsESStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsESStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsES struct {
	Standard *TaxRegistrationCountryOptionsESStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsESType `json:"type"`
}
type TaxRegistrationCountryOptionsET struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsETType `json:"type"`
}
type TaxRegistrationCountryOptionsFIStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsFIStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsFI struct {
	Standard *TaxRegistrationCountryOptionsFIStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsFIType `json:"type"`
}
type TaxRegistrationCountryOptionsFRStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsFRStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsFR struct {
	Standard *TaxRegistrationCountryOptionsFRStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsFRType `json:"type"`
}
type TaxRegistrationCountryOptionsGBStandard struct {
	// Place of supply scheme used in an Default standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsGBStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsGB struct {
	Standard *TaxRegistrationCountryOptionsGBStandard `json:"standard"`
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsGBType `json:"type"`
}
type TaxRegistrationCountryOptionsGe struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsGeType `json:"type"`
}
type TaxRegistrationCountryOptionsGn struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsGnType `json:"type"`
}
type TaxRegistrationCountryOptionsGrStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsGrStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsGr struct {
	Standard *TaxRegistrationCountryOptionsGrStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsGrType `json:"type"`
}
type TaxRegistrationCountryOptionsHRStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsHRStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsHR struct {
	Standard *TaxRegistrationCountryOptionsHRStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsHRType `json:"type"`
}
type TaxRegistrationCountryOptionsHUStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsHUStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsHU struct {
	Standard *TaxRegistrationCountryOptionsHUStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsHUType `json:"type"`
}
type TaxRegistrationCountryOptionsID struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsIDType `json:"type"`
}
type TaxRegistrationCountryOptionsIeStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsIeStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsIe struct {
	Standard *TaxRegistrationCountryOptionsIeStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsIeType `json:"type"`
}
type TaxRegistrationCountryOptionsIn struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsInType `json:"type"`
}
type TaxRegistrationCountryOptionsIs struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsIsType `json:"type"`
}
type TaxRegistrationCountryOptionsITStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsITStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsIT struct {
	Standard *TaxRegistrationCountryOptionsITStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsITType `json:"type"`
}
type TaxRegistrationCountryOptionsJPStandard struct {
	// Place of supply scheme used in an Default standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsJPStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsJP struct {
	Standard *TaxRegistrationCountryOptionsJPStandard `json:"standard"`
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsJPType `json:"type"`
}
type TaxRegistrationCountryOptionsKe struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsKeType `json:"type"`
}
type TaxRegistrationCountryOptionsKg struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsKgType `json:"type"`
}
type TaxRegistrationCountryOptionsKh struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsKhType `json:"type"`
}
type TaxRegistrationCountryOptionsKr struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsKrType `json:"type"`
}
type TaxRegistrationCountryOptionsKz struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsKzType `json:"type"`
}
type TaxRegistrationCountryOptionsLa struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsLaType `json:"type"`
}
type TaxRegistrationCountryOptionsLTStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsLTStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsLT struct {
	Standard *TaxRegistrationCountryOptionsLTStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsLTType `json:"type"`
}
type TaxRegistrationCountryOptionsLuStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsLuStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsLu struct {
	Standard *TaxRegistrationCountryOptionsLuStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsLuType `json:"type"`
}
type TaxRegistrationCountryOptionsLVStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsLVStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsLV struct {
	Standard *TaxRegistrationCountryOptionsLVStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsLVType `json:"type"`
}
type TaxRegistrationCountryOptionsMa struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsMaType `json:"type"`
}
type TaxRegistrationCountryOptionsMd struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsMdType `json:"type"`
}
type TaxRegistrationCountryOptionsMe struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsMeType `json:"type"`
}
type TaxRegistrationCountryOptionsMk struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsMkType `json:"type"`
}
type TaxRegistrationCountryOptionsMr struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsMrType `json:"type"`
}
type TaxRegistrationCountryOptionsMTStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsMTStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsMT struct {
	Standard *TaxRegistrationCountryOptionsMTStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsMTType `json:"type"`
}
type TaxRegistrationCountryOptionsMX struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsMXType `json:"type"`
}
type TaxRegistrationCountryOptionsMy struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsMyType `json:"type"`
}
type TaxRegistrationCountryOptionsNg struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsNgType `json:"type"`
}
type TaxRegistrationCountryOptionsNLStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsNLStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsNL struct {
	Standard *TaxRegistrationCountryOptionsNLStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsNLType `json:"type"`
}
type TaxRegistrationCountryOptionsNoStandard struct {
	// Place of supply scheme used in an Default standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsNoStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsNo struct {
	Standard *TaxRegistrationCountryOptionsNoStandard `json:"standard"`
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsNoType `json:"type"`
}
type TaxRegistrationCountryOptionsNp struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsNpType `json:"type"`
}
type TaxRegistrationCountryOptionsNzStandard struct {
	// Place of supply scheme used in an Default standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsNzStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsNz struct {
	Standard *TaxRegistrationCountryOptionsNzStandard `json:"standard"`
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsNzType `json:"type"`
}
type TaxRegistrationCountryOptionsOm struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsOmType `json:"type"`
}
type TaxRegistrationCountryOptionsPe struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsPeType `json:"type"`
}
type TaxRegistrationCountryOptionsPh struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsPhType `json:"type"`
}
type TaxRegistrationCountryOptionsPLStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsPLStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsPL struct {
	Standard *TaxRegistrationCountryOptionsPLStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsPLType `json:"type"`
}
type TaxRegistrationCountryOptionsPTStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsPTStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsPT struct {
	Standard *TaxRegistrationCountryOptionsPTStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsPTType `json:"type"`
}
type TaxRegistrationCountryOptionsROStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsROStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsRO struct {
	Standard *TaxRegistrationCountryOptionsROStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsROType `json:"type"`
}
type TaxRegistrationCountryOptionsRs struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsRsType `json:"type"`
}
type TaxRegistrationCountryOptionsRU struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsRUType `json:"type"`
}
type TaxRegistrationCountryOptionsSa struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsSaType `json:"type"`
}
type TaxRegistrationCountryOptionsSeStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsSeStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsSe struct {
	Standard *TaxRegistrationCountryOptionsSeStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsSeType `json:"type"`
}
type TaxRegistrationCountryOptionsSgStandard struct {
	// Place of supply scheme used in an Default standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsSgStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsSg struct {
	Standard *TaxRegistrationCountryOptionsSgStandard `json:"standard"`
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsSgType `json:"type"`
}
type TaxRegistrationCountryOptionsSiStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsSiStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsSi struct {
	Standard *TaxRegistrationCountryOptionsSiStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsSiType `json:"type"`
}
type TaxRegistrationCountryOptionsSKStandard struct {
	// Place of supply scheme used in an EU standard registration.
	PlaceOfSupplyScheme TaxRegistrationCountryOptionsSKStandardPlaceOfSupplyScheme `json:"place_of_supply_scheme"`
}
type TaxRegistrationCountryOptionsSK struct {
	Standard *TaxRegistrationCountryOptionsSKStandard `json:"standard"`
	// Type of registration in an EU country.
	Type TaxRegistrationCountryOptionsSKType `json:"type"`
}
type TaxRegistrationCountryOptionsSn struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsSnType `json:"type"`
}
type TaxRegistrationCountryOptionsSr struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsSrType `json:"type"`
}
type TaxRegistrationCountryOptionsTH struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsTHType `json:"type"`
}
type TaxRegistrationCountryOptionsTj struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsTjType `json:"type"`
}
type TaxRegistrationCountryOptionsTR struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsTRType `json:"type"`
}
type TaxRegistrationCountryOptionsTz struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsTzType `json:"type"`
}
type TaxRegistrationCountryOptionsUa struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsUaType `json:"type"`
}
type TaxRegistrationCountryOptionsUg struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsUgType `json:"type"`
}
type TaxRegistrationCountryOptionsUSLocalAmusementTax struct {
	// A [FIPS code](https://www.census.gov/library/reference/code-lists/ansi.html) representing the local jurisdiction.
	Jurisdiction string `json:"jurisdiction"`
}
type TaxRegistrationCountryOptionsUSLocalLeaseTax struct {
	// A [FIPS code](https://www.census.gov/library/reference/code-lists/ansi.html) representing the local jurisdiction.
	Jurisdiction string `json:"jurisdiction"`
}

// Elections for the state sales tax registration.
type TaxRegistrationCountryOptionsUSStateSalesTaxElection struct {
	// A [FIPS code](https://www.census.gov/library/reference/code-lists/ansi.html) representing the local jurisdiction.
	Jurisdiction string `json:"jurisdiction"`
	// The type of the election for the state sales tax registration.
	Type TaxRegistrationCountryOptionsUSStateSalesTaxElectionType `json:"type"`
}
type TaxRegistrationCountryOptionsUSStateSalesTax struct {
	// Elections for the state sales tax registration.
	Elections []*TaxRegistrationCountryOptionsUSStateSalesTaxElection `json:"elections"`
}
type TaxRegistrationCountryOptionsUS struct {
	LocalAmusementTax *TaxRegistrationCountryOptionsUSLocalAmusementTax `json:"local_amusement_tax"`
	LocalLeaseTax     *TaxRegistrationCountryOptionsUSLocalLeaseTax     `json:"local_lease_tax"`
	// Two-letter US state code ([ISO 3166-2](https://en.wikipedia.org/wiki/ISO_3166-2)).
	State         string                                        `json:"state"`
	StateSalesTax *TaxRegistrationCountryOptionsUSStateSalesTax `json:"state_sales_tax"`
	// Type of registration in the US.
	Type TaxRegistrationCountryOptionsUSType `json:"type"`
}
type TaxRegistrationCountryOptionsUy struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsUyType `json:"type"`
}
type TaxRegistrationCountryOptionsUz struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsUzType `json:"type"`
}
type TaxRegistrationCountryOptionsVn struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsVnType `json:"type"`
}
type TaxRegistrationCountryOptionsZa struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsZaType `json:"type"`
}
type TaxRegistrationCountryOptionsZm struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsZmType `json:"type"`
}
type TaxRegistrationCountryOptionsZw struct {
	// Type of registration in `country`.
	Type TaxRegistrationCountryOptionsZwType `json:"type"`
}
type TaxRegistrationCountryOptions struct {
	Ae *TaxRegistrationCountryOptionsAe `json:"ae"`
	Al *TaxRegistrationCountryOptionsAl `json:"al"`
	Am *TaxRegistrationCountryOptionsAm `json:"am"`
	Ao *TaxRegistrationCountryOptionsAo `json:"ao"`
	At *TaxRegistrationCountryOptionsAt `json:"at"`
	Au *TaxRegistrationCountryOptionsAu `json:"au"`
	Aw *TaxRegistrationCountryOptionsAw `json:"aw"`
	Az *TaxRegistrationCountryOptionsAz `json:"az"`
	Ba *TaxRegistrationCountryOptionsBa `json:"ba"`
	Bb *TaxRegistrationCountryOptionsBb `json:"bb"`
	Bd *TaxRegistrationCountryOptionsBd `json:"bd"`
	Be *TaxRegistrationCountryOptionsBe `json:"be"`
	Bf *TaxRegistrationCountryOptionsBf `json:"bf"`
	BG *TaxRegistrationCountryOptionsBG `json:"bg"`
	Bh *TaxRegistrationCountryOptionsBh `json:"bh"`
	Bj *TaxRegistrationCountryOptionsBj `json:"bj"`
	Bs *TaxRegistrationCountryOptionsBs `json:"bs"`
	By *TaxRegistrationCountryOptionsBy `json:"by"`
	Ca *TaxRegistrationCountryOptionsCa `json:"ca"`
	Cd *TaxRegistrationCountryOptionsCd `json:"cd"`
	Ch *TaxRegistrationCountryOptionsCh `json:"ch"`
	Cl *TaxRegistrationCountryOptionsCl `json:"cl"`
	Cm *TaxRegistrationCountryOptionsCm `json:"cm"`
	Co *TaxRegistrationCountryOptionsCo `json:"co"`
	Cr *TaxRegistrationCountryOptionsCr `json:"cr"`
	Cv *TaxRegistrationCountryOptionsCv `json:"cv"`
	Cy *TaxRegistrationCountryOptionsCy `json:"cy"`
	Cz *TaxRegistrationCountryOptionsCz `json:"cz"`
	DE *TaxRegistrationCountryOptionsDE `json:"de"`
	Dk *TaxRegistrationCountryOptionsDk `json:"dk"`
	Ec *TaxRegistrationCountryOptionsEc `json:"ec"`
	Ee *TaxRegistrationCountryOptionsEe `json:"ee"`
	Eg *TaxRegistrationCountryOptionsEg `json:"eg"`
	ES *TaxRegistrationCountryOptionsES `json:"es"`
	ET *TaxRegistrationCountryOptionsET `json:"et"`
	FI *TaxRegistrationCountryOptionsFI `json:"fi"`
	FR *TaxRegistrationCountryOptionsFR `json:"fr"`
	GB *TaxRegistrationCountryOptionsGB `json:"gb"`
	Ge *TaxRegistrationCountryOptionsGe `json:"ge"`
	Gn *TaxRegistrationCountryOptionsGn `json:"gn"`
	Gr *TaxRegistrationCountryOptionsGr `json:"gr"`
	HR *TaxRegistrationCountryOptionsHR `json:"hr"`
	HU *TaxRegistrationCountryOptionsHU `json:"hu"`
	ID *TaxRegistrationCountryOptionsID `json:"id"`
	Ie *TaxRegistrationCountryOptionsIe `json:"ie"`
	In *TaxRegistrationCountryOptionsIn `json:"in"`
	Is *TaxRegistrationCountryOptionsIs `json:"is"`
	IT *TaxRegistrationCountryOptionsIT `json:"it"`
	JP *TaxRegistrationCountryOptionsJP `json:"jp"`
	Ke *TaxRegistrationCountryOptionsKe `json:"ke"`
	Kg *TaxRegistrationCountryOptionsKg `json:"kg"`
	Kh *TaxRegistrationCountryOptionsKh `json:"kh"`
	Kr *TaxRegistrationCountryOptionsKr `json:"kr"`
	Kz *TaxRegistrationCountryOptionsKz `json:"kz"`
	La *TaxRegistrationCountryOptionsLa `json:"la"`
	LT *TaxRegistrationCountryOptionsLT `json:"lt"`
	Lu *TaxRegistrationCountryOptionsLu `json:"lu"`
	LV *TaxRegistrationCountryOptionsLV `json:"lv"`
	Ma *TaxRegistrationCountryOptionsMa `json:"ma"`
	Md *TaxRegistrationCountryOptionsMd `json:"md"`
	Me *TaxRegistrationCountryOptionsMe `json:"me"`
	Mk *TaxRegistrationCountryOptionsMk `json:"mk"`
	Mr *TaxRegistrationCountryOptionsMr `json:"mr"`
	MT *TaxRegistrationCountryOptionsMT `json:"mt"`
	MX *TaxRegistrationCountryOptionsMX `json:"mx"`
	My *TaxRegistrationCountryOptionsMy `json:"my"`
	Ng *TaxRegistrationCountryOptionsNg `json:"ng"`
	NL *TaxRegistrationCountryOptionsNL `json:"nl"`
	No *TaxRegistrationCountryOptionsNo `json:"no"`
	Np *TaxRegistrationCountryOptionsNp `json:"np"`
	Nz *TaxRegistrationCountryOptionsNz `json:"nz"`
	Om *TaxRegistrationCountryOptionsOm `json:"om"`
	Pe *TaxRegistrationCountryOptionsPe `json:"pe"`
	Ph *TaxRegistrationCountryOptionsPh `json:"ph"`
	PL *TaxRegistrationCountryOptionsPL `json:"pl"`
	PT *TaxRegistrationCountryOptionsPT `json:"pt"`
	RO *TaxRegistrationCountryOptionsRO `json:"ro"`
	Rs *TaxRegistrationCountryOptionsRs `json:"rs"`
	RU *TaxRegistrationCountryOptionsRU `json:"ru"`
	Sa *TaxRegistrationCountryOptionsSa `json:"sa"`
	Se *TaxRegistrationCountryOptionsSe `json:"se"`
	Sg *TaxRegistrationCountryOptionsSg `json:"sg"`
	Si *TaxRegistrationCountryOptionsSi `json:"si"`
	SK *TaxRegistrationCountryOptionsSK `json:"sk"`
	Sn *TaxRegistrationCountryOptionsSn `json:"sn"`
	Sr *TaxRegistrationCountryOptionsSr `json:"sr"`
	TH *TaxRegistrationCountryOptionsTH `json:"th"`
	Tj *TaxRegistrationCountryOptionsTj `json:"tj"`
	TR *TaxRegistrationCountryOptionsTR `json:"tr"`
	Tz *TaxRegistrationCountryOptionsTz `json:"tz"`
	Ua *TaxRegistrationCountryOptionsUa `json:"ua"`
	Ug *TaxRegistrationCountryOptionsUg `json:"ug"`
	US *TaxRegistrationCountryOptionsUS `json:"us"`
	Uy *TaxRegistrationCountryOptionsUy `json:"uy"`
	Uz *TaxRegistrationCountryOptionsUz `json:"uz"`
	Vn *TaxRegistrationCountryOptionsVn `json:"vn"`
	Za *TaxRegistrationCountryOptionsZa `json:"za"`
	Zm *TaxRegistrationCountryOptionsZm `json:"zm"`
	Zw *TaxRegistrationCountryOptionsZw `json:"zw"`
}

// A Tax `Registration` lets us know that your business is registered to collect tax on payments within a region, enabling you to [automatically collect tax](https://stripe.com/docs/tax).
//
// Stripe doesn't register on your behalf with the relevant authorities when you create a Tax `Registration` object. For more information on how to register to collect tax, see [our guide](https://stripe.com/docs/tax/registering).
//
// Related guide: [Using the Registrations API](https://stripe.com/docs/tax/registrations-api)
type TaxRegistration struct {
	APIResource
	// Time at which the registration becomes active. Measured in seconds since the Unix epoch.
	ActiveFrom int64 `json:"active_from"`
	// Two-letter country code ([ISO 3166-1 alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)).
	Country        string                         `json:"country"`
	CountryOptions *TaxRegistrationCountryOptions `json:"country_options"`
	// Time at which the object was created. Measured in seconds since the Unix epoch.
	Created int64 `json:"created"`
	// If set, the registration stops being active at this time. If not set, the registration will be active indefinitely. Measured in seconds since the Unix epoch.
	ExpiresAt int64 `json:"expires_at"`
	// Unique identifier for the object.
	ID string `json:"id"`
	// Has the value `true` if the object exists in live mode or the value `false` if the object exists in test mode.
	Livemode bool `json:"livemode"`
	// String representing the object's type. Objects of the same type share the same value.
	Object string `json:"object"`
	// The status of the registration. This field is present for convenience and can be deduced from `active_from` and `expires_at`.
	Status TaxRegistrationStatus `json:"status"`
}

// TaxRegistrationList is a list of Registrations as retrieved from a list endpoint.
type TaxRegistrationList struct {
	APIResource
	ListMeta
	Data []*TaxRegistration `json:"data"`
}
