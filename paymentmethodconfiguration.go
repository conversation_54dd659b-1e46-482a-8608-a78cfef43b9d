//
//
// File generated from our OpenAPI spec
//
//

package stripe

// The account's display preference.
type PaymentMethodConfigurationACSSDebitDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationACSSDebitDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationACSSDebitDisplayPreferencePreferenceNone PaymentMethodConfigurationACSSDebitDisplayPreferencePreference = "none"
	PaymentMethodConfigurationACSSDebitDisplayPreferencePreferenceOff  PaymentMethodConfigurationACSSDebitDisplayPreferencePreference = "off"
	PaymentMethodConfigurationACSSDebitDisplayPreferencePreferenceOn   PaymentMethodConfigurationACSSDebitDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationACSSDebitDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationACSSDebitDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationACSSDebitDisplayPreferenceValueOff PaymentMethodConfigurationACSSDebitDisplayPreferenceValue = "off"
	PaymentMethodConfigurationACSSDebitDisplayPreferenceValueOn  PaymentMethodConfigurationACSSDebitDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationAffirmDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationAffirmDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationAffirmDisplayPreferencePreferenceNone PaymentMethodConfigurationAffirmDisplayPreferencePreference = "none"
	PaymentMethodConfigurationAffirmDisplayPreferencePreferenceOff  PaymentMethodConfigurationAffirmDisplayPreferencePreference = "off"
	PaymentMethodConfigurationAffirmDisplayPreferencePreferenceOn   PaymentMethodConfigurationAffirmDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationAffirmDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationAffirmDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationAffirmDisplayPreferenceValueOff PaymentMethodConfigurationAffirmDisplayPreferenceValue = "off"
	PaymentMethodConfigurationAffirmDisplayPreferenceValueOn  PaymentMethodConfigurationAffirmDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationAfterpayClearpayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationAfterpayClearpayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationAfterpayClearpayDisplayPreferencePreferenceNone PaymentMethodConfigurationAfterpayClearpayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationAfterpayClearpayDisplayPreferencePreferenceOff  PaymentMethodConfigurationAfterpayClearpayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationAfterpayClearpayDisplayPreferencePreferenceOn   PaymentMethodConfigurationAfterpayClearpayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationAfterpayClearpayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationAfterpayClearpayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationAfterpayClearpayDisplayPreferenceValueOff PaymentMethodConfigurationAfterpayClearpayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationAfterpayClearpayDisplayPreferenceValueOn  PaymentMethodConfigurationAfterpayClearpayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationAlipayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationAlipayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationAlipayDisplayPreferencePreferenceNone PaymentMethodConfigurationAlipayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationAlipayDisplayPreferencePreferenceOff  PaymentMethodConfigurationAlipayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationAlipayDisplayPreferencePreferenceOn   PaymentMethodConfigurationAlipayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationAlipayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationAlipayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationAlipayDisplayPreferenceValueOff PaymentMethodConfigurationAlipayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationAlipayDisplayPreferenceValueOn  PaymentMethodConfigurationAlipayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationAlmaDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationAlmaDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationAlmaDisplayPreferencePreferenceNone PaymentMethodConfigurationAlmaDisplayPreferencePreference = "none"
	PaymentMethodConfigurationAlmaDisplayPreferencePreferenceOff  PaymentMethodConfigurationAlmaDisplayPreferencePreference = "off"
	PaymentMethodConfigurationAlmaDisplayPreferencePreferenceOn   PaymentMethodConfigurationAlmaDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationAlmaDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationAlmaDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationAlmaDisplayPreferenceValueOff PaymentMethodConfigurationAlmaDisplayPreferenceValue = "off"
	PaymentMethodConfigurationAlmaDisplayPreferenceValueOn  PaymentMethodConfigurationAlmaDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationAmazonPayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationAmazonPayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationAmazonPayDisplayPreferencePreferenceNone PaymentMethodConfigurationAmazonPayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationAmazonPayDisplayPreferencePreferenceOff  PaymentMethodConfigurationAmazonPayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationAmazonPayDisplayPreferencePreferenceOn   PaymentMethodConfigurationAmazonPayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationAmazonPayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationAmazonPayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationAmazonPayDisplayPreferenceValueOff PaymentMethodConfigurationAmazonPayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationAmazonPayDisplayPreferenceValueOn  PaymentMethodConfigurationAmazonPayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationApplePayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationApplePayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationApplePayDisplayPreferencePreferenceNone PaymentMethodConfigurationApplePayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationApplePayDisplayPreferencePreferenceOff  PaymentMethodConfigurationApplePayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationApplePayDisplayPreferencePreferenceOn   PaymentMethodConfigurationApplePayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationApplePayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationApplePayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationApplePayDisplayPreferenceValueOff PaymentMethodConfigurationApplePayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationApplePayDisplayPreferenceValueOn  PaymentMethodConfigurationApplePayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationAUBECSDebitDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationAUBECSDebitDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationAUBECSDebitDisplayPreferencePreferenceNone PaymentMethodConfigurationAUBECSDebitDisplayPreferencePreference = "none"
	PaymentMethodConfigurationAUBECSDebitDisplayPreferencePreferenceOff  PaymentMethodConfigurationAUBECSDebitDisplayPreferencePreference = "off"
	PaymentMethodConfigurationAUBECSDebitDisplayPreferencePreferenceOn   PaymentMethodConfigurationAUBECSDebitDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationAUBECSDebitDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationAUBECSDebitDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationAUBECSDebitDisplayPreferenceValueOff PaymentMethodConfigurationAUBECSDebitDisplayPreferenceValue = "off"
	PaymentMethodConfigurationAUBECSDebitDisplayPreferenceValueOn  PaymentMethodConfigurationAUBECSDebitDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationBACSDebitDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationBACSDebitDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationBACSDebitDisplayPreferencePreferenceNone PaymentMethodConfigurationBACSDebitDisplayPreferencePreference = "none"
	PaymentMethodConfigurationBACSDebitDisplayPreferencePreferenceOff  PaymentMethodConfigurationBACSDebitDisplayPreferencePreference = "off"
	PaymentMethodConfigurationBACSDebitDisplayPreferencePreferenceOn   PaymentMethodConfigurationBACSDebitDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationBACSDebitDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationBACSDebitDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationBACSDebitDisplayPreferenceValueOff PaymentMethodConfigurationBACSDebitDisplayPreferenceValue = "off"
	PaymentMethodConfigurationBACSDebitDisplayPreferenceValueOn  PaymentMethodConfigurationBACSDebitDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationBancontactDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationBancontactDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationBancontactDisplayPreferencePreferenceNone PaymentMethodConfigurationBancontactDisplayPreferencePreference = "none"
	PaymentMethodConfigurationBancontactDisplayPreferencePreferenceOff  PaymentMethodConfigurationBancontactDisplayPreferencePreference = "off"
	PaymentMethodConfigurationBancontactDisplayPreferencePreferenceOn   PaymentMethodConfigurationBancontactDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationBancontactDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationBancontactDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationBancontactDisplayPreferenceValueOff PaymentMethodConfigurationBancontactDisplayPreferenceValue = "off"
	PaymentMethodConfigurationBancontactDisplayPreferenceValueOn  PaymentMethodConfigurationBancontactDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationBillieDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationBillieDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationBillieDisplayPreferencePreferenceNone PaymentMethodConfigurationBillieDisplayPreferencePreference = "none"
	PaymentMethodConfigurationBillieDisplayPreferencePreferenceOff  PaymentMethodConfigurationBillieDisplayPreferencePreference = "off"
	PaymentMethodConfigurationBillieDisplayPreferencePreferenceOn   PaymentMethodConfigurationBillieDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationBillieDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationBillieDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationBillieDisplayPreferenceValueOff PaymentMethodConfigurationBillieDisplayPreferenceValue = "off"
	PaymentMethodConfigurationBillieDisplayPreferenceValueOn  PaymentMethodConfigurationBillieDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationBLIKDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationBLIKDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationBLIKDisplayPreferencePreferenceNone PaymentMethodConfigurationBLIKDisplayPreferencePreference = "none"
	PaymentMethodConfigurationBLIKDisplayPreferencePreferenceOff  PaymentMethodConfigurationBLIKDisplayPreferencePreference = "off"
	PaymentMethodConfigurationBLIKDisplayPreferencePreferenceOn   PaymentMethodConfigurationBLIKDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationBLIKDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationBLIKDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationBLIKDisplayPreferenceValueOff PaymentMethodConfigurationBLIKDisplayPreferenceValue = "off"
	PaymentMethodConfigurationBLIKDisplayPreferenceValueOn  PaymentMethodConfigurationBLIKDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationBoletoDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationBoletoDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationBoletoDisplayPreferencePreferenceNone PaymentMethodConfigurationBoletoDisplayPreferencePreference = "none"
	PaymentMethodConfigurationBoletoDisplayPreferencePreferenceOff  PaymentMethodConfigurationBoletoDisplayPreferencePreference = "off"
	PaymentMethodConfigurationBoletoDisplayPreferencePreferenceOn   PaymentMethodConfigurationBoletoDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationBoletoDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationBoletoDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationBoletoDisplayPreferenceValueOff PaymentMethodConfigurationBoletoDisplayPreferenceValue = "off"
	PaymentMethodConfigurationBoletoDisplayPreferenceValueOn  PaymentMethodConfigurationBoletoDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationCardDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationCardDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationCardDisplayPreferencePreferenceNone PaymentMethodConfigurationCardDisplayPreferencePreference = "none"
	PaymentMethodConfigurationCardDisplayPreferencePreferenceOff  PaymentMethodConfigurationCardDisplayPreferencePreference = "off"
	PaymentMethodConfigurationCardDisplayPreferencePreferenceOn   PaymentMethodConfigurationCardDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationCardDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationCardDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationCardDisplayPreferenceValueOff PaymentMethodConfigurationCardDisplayPreferenceValue = "off"
	PaymentMethodConfigurationCardDisplayPreferenceValueOn  PaymentMethodConfigurationCardDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationCartesBancairesDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationCartesBancairesDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationCartesBancairesDisplayPreferencePreferenceNone PaymentMethodConfigurationCartesBancairesDisplayPreferencePreference = "none"
	PaymentMethodConfigurationCartesBancairesDisplayPreferencePreferenceOff  PaymentMethodConfigurationCartesBancairesDisplayPreferencePreference = "off"
	PaymentMethodConfigurationCartesBancairesDisplayPreferencePreferenceOn   PaymentMethodConfigurationCartesBancairesDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationCartesBancairesDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationCartesBancairesDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationCartesBancairesDisplayPreferenceValueOff PaymentMethodConfigurationCartesBancairesDisplayPreferenceValue = "off"
	PaymentMethodConfigurationCartesBancairesDisplayPreferenceValueOn  PaymentMethodConfigurationCartesBancairesDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationCashAppDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationCashAppDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationCashAppDisplayPreferencePreferenceNone PaymentMethodConfigurationCashAppDisplayPreferencePreference = "none"
	PaymentMethodConfigurationCashAppDisplayPreferencePreferenceOff  PaymentMethodConfigurationCashAppDisplayPreferencePreference = "off"
	PaymentMethodConfigurationCashAppDisplayPreferencePreferenceOn   PaymentMethodConfigurationCashAppDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationCashAppDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationCashAppDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationCashAppDisplayPreferenceValueOff PaymentMethodConfigurationCashAppDisplayPreferenceValue = "off"
	PaymentMethodConfigurationCashAppDisplayPreferenceValueOn  PaymentMethodConfigurationCashAppDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationCustomerBalanceDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationCustomerBalanceDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationCustomerBalanceDisplayPreferencePreferenceNone PaymentMethodConfigurationCustomerBalanceDisplayPreferencePreference = "none"
	PaymentMethodConfigurationCustomerBalanceDisplayPreferencePreferenceOff  PaymentMethodConfigurationCustomerBalanceDisplayPreferencePreference = "off"
	PaymentMethodConfigurationCustomerBalanceDisplayPreferencePreferenceOn   PaymentMethodConfigurationCustomerBalanceDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationCustomerBalanceDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationCustomerBalanceDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationCustomerBalanceDisplayPreferenceValueOff PaymentMethodConfigurationCustomerBalanceDisplayPreferenceValue = "off"
	PaymentMethodConfigurationCustomerBalanceDisplayPreferenceValueOn  PaymentMethodConfigurationCustomerBalanceDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationEPSDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationEPSDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationEPSDisplayPreferencePreferenceNone PaymentMethodConfigurationEPSDisplayPreferencePreference = "none"
	PaymentMethodConfigurationEPSDisplayPreferencePreferenceOff  PaymentMethodConfigurationEPSDisplayPreferencePreference = "off"
	PaymentMethodConfigurationEPSDisplayPreferencePreferenceOn   PaymentMethodConfigurationEPSDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationEPSDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationEPSDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationEPSDisplayPreferenceValueOff PaymentMethodConfigurationEPSDisplayPreferenceValue = "off"
	PaymentMethodConfigurationEPSDisplayPreferenceValueOn  PaymentMethodConfigurationEPSDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationFPXDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationFPXDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationFPXDisplayPreferencePreferenceNone PaymentMethodConfigurationFPXDisplayPreferencePreference = "none"
	PaymentMethodConfigurationFPXDisplayPreferencePreferenceOff  PaymentMethodConfigurationFPXDisplayPreferencePreference = "off"
	PaymentMethodConfigurationFPXDisplayPreferencePreferenceOn   PaymentMethodConfigurationFPXDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationFPXDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationFPXDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationFPXDisplayPreferenceValueOff PaymentMethodConfigurationFPXDisplayPreferenceValue = "off"
	PaymentMethodConfigurationFPXDisplayPreferenceValueOn  PaymentMethodConfigurationFPXDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationGiropayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationGiropayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationGiropayDisplayPreferencePreferenceNone PaymentMethodConfigurationGiropayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationGiropayDisplayPreferencePreferenceOff  PaymentMethodConfigurationGiropayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationGiropayDisplayPreferencePreferenceOn   PaymentMethodConfigurationGiropayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationGiropayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationGiropayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationGiropayDisplayPreferenceValueOff PaymentMethodConfigurationGiropayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationGiropayDisplayPreferenceValueOn  PaymentMethodConfigurationGiropayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationGooglePayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationGooglePayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationGooglePayDisplayPreferencePreferenceNone PaymentMethodConfigurationGooglePayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationGooglePayDisplayPreferencePreferenceOff  PaymentMethodConfigurationGooglePayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationGooglePayDisplayPreferencePreferenceOn   PaymentMethodConfigurationGooglePayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationGooglePayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationGooglePayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationGooglePayDisplayPreferenceValueOff PaymentMethodConfigurationGooglePayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationGooglePayDisplayPreferenceValueOn  PaymentMethodConfigurationGooglePayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationGrabpayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationGrabpayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationGrabpayDisplayPreferencePreferenceNone PaymentMethodConfigurationGrabpayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationGrabpayDisplayPreferencePreferenceOff  PaymentMethodConfigurationGrabpayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationGrabpayDisplayPreferencePreferenceOn   PaymentMethodConfigurationGrabpayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationGrabpayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationGrabpayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationGrabpayDisplayPreferenceValueOff PaymentMethodConfigurationGrabpayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationGrabpayDisplayPreferenceValueOn  PaymentMethodConfigurationGrabpayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationIDEALDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationIDEALDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationIDEALDisplayPreferencePreferenceNone PaymentMethodConfigurationIDEALDisplayPreferencePreference = "none"
	PaymentMethodConfigurationIDEALDisplayPreferencePreferenceOff  PaymentMethodConfigurationIDEALDisplayPreferencePreference = "off"
	PaymentMethodConfigurationIDEALDisplayPreferencePreferenceOn   PaymentMethodConfigurationIDEALDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationIDEALDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationIDEALDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationIDEALDisplayPreferenceValueOff PaymentMethodConfigurationIDEALDisplayPreferenceValue = "off"
	PaymentMethodConfigurationIDEALDisplayPreferenceValueOn  PaymentMethodConfigurationIDEALDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationJCBDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationJCBDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationJCBDisplayPreferencePreferenceNone PaymentMethodConfigurationJCBDisplayPreferencePreference = "none"
	PaymentMethodConfigurationJCBDisplayPreferencePreferenceOff  PaymentMethodConfigurationJCBDisplayPreferencePreference = "off"
	PaymentMethodConfigurationJCBDisplayPreferencePreferenceOn   PaymentMethodConfigurationJCBDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationJCBDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationJCBDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationJCBDisplayPreferenceValueOff PaymentMethodConfigurationJCBDisplayPreferenceValue = "off"
	PaymentMethodConfigurationJCBDisplayPreferenceValueOn  PaymentMethodConfigurationJCBDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationKakaoPayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationKakaoPayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationKakaoPayDisplayPreferencePreferenceNone PaymentMethodConfigurationKakaoPayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationKakaoPayDisplayPreferencePreferenceOff  PaymentMethodConfigurationKakaoPayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationKakaoPayDisplayPreferencePreferenceOn   PaymentMethodConfigurationKakaoPayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationKakaoPayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationKakaoPayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationKakaoPayDisplayPreferenceValueOff PaymentMethodConfigurationKakaoPayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationKakaoPayDisplayPreferenceValueOn  PaymentMethodConfigurationKakaoPayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationKlarnaDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationKlarnaDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationKlarnaDisplayPreferencePreferenceNone PaymentMethodConfigurationKlarnaDisplayPreferencePreference = "none"
	PaymentMethodConfigurationKlarnaDisplayPreferencePreferenceOff  PaymentMethodConfigurationKlarnaDisplayPreferencePreference = "off"
	PaymentMethodConfigurationKlarnaDisplayPreferencePreferenceOn   PaymentMethodConfigurationKlarnaDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationKlarnaDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationKlarnaDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationKlarnaDisplayPreferenceValueOff PaymentMethodConfigurationKlarnaDisplayPreferenceValue = "off"
	PaymentMethodConfigurationKlarnaDisplayPreferenceValueOn  PaymentMethodConfigurationKlarnaDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationKonbiniDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationKonbiniDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationKonbiniDisplayPreferencePreferenceNone PaymentMethodConfigurationKonbiniDisplayPreferencePreference = "none"
	PaymentMethodConfigurationKonbiniDisplayPreferencePreferenceOff  PaymentMethodConfigurationKonbiniDisplayPreferencePreference = "off"
	PaymentMethodConfigurationKonbiniDisplayPreferencePreferenceOn   PaymentMethodConfigurationKonbiniDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationKonbiniDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationKonbiniDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationKonbiniDisplayPreferenceValueOff PaymentMethodConfigurationKonbiniDisplayPreferenceValue = "off"
	PaymentMethodConfigurationKonbiniDisplayPreferenceValueOn  PaymentMethodConfigurationKonbiniDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationKrCardDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationKrCardDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationKrCardDisplayPreferencePreferenceNone PaymentMethodConfigurationKrCardDisplayPreferencePreference = "none"
	PaymentMethodConfigurationKrCardDisplayPreferencePreferenceOff  PaymentMethodConfigurationKrCardDisplayPreferencePreference = "off"
	PaymentMethodConfigurationKrCardDisplayPreferencePreferenceOn   PaymentMethodConfigurationKrCardDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationKrCardDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationKrCardDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationKrCardDisplayPreferenceValueOff PaymentMethodConfigurationKrCardDisplayPreferenceValue = "off"
	PaymentMethodConfigurationKrCardDisplayPreferenceValueOn  PaymentMethodConfigurationKrCardDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationLinkDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationLinkDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationLinkDisplayPreferencePreferenceNone PaymentMethodConfigurationLinkDisplayPreferencePreference = "none"
	PaymentMethodConfigurationLinkDisplayPreferencePreferenceOff  PaymentMethodConfigurationLinkDisplayPreferencePreference = "off"
	PaymentMethodConfigurationLinkDisplayPreferencePreferenceOn   PaymentMethodConfigurationLinkDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationLinkDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationLinkDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationLinkDisplayPreferenceValueOff PaymentMethodConfigurationLinkDisplayPreferenceValue = "off"
	PaymentMethodConfigurationLinkDisplayPreferenceValueOn  PaymentMethodConfigurationLinkDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationMobilepayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationMobilepayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationMobilepayDisplayPreferencePreferenceNone PaymentMethodConfigurationMobilepayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationMobilepayDisplayPreferencePreferenceOff  PaymentMethodConfigurationMobilepayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationMobilepayDisplayPreferencePreferenceOn   PaymentMethodConfigurationMobilepayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationMobilepayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationMobilepayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationMobilepayDisplayPreferenceValueOff PaymentMethodConfigurationMobilepayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationMobilepayDisplayPreferenceValueOn  PaymentMethodConfigurationMobilepayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationMultibancoDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationMultibancoDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationMultibancoDisplayPreferencePreferenceNone PaymentMethodConfigurationMultibancoDisplayPreferencePreference = "none"
	PaymentMethodConfigurationMultibancoDisplayPreferencePreferenceOff  PaymentMethodConfigurationMultibancoDisplayPreferencePreference = "off"
	PaymentMethodConfigurationMultibancoDisplayPreferencePreferenceOn   PaymentMethodConfigurationMultibancoDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationMultibancoDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationMultibancoDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationMultibancoDisplayPreferenceValueOff PaymentMethodConfigurationMultibancoDisplayPreferenceValue = "off"
	PaymentMethodConfigurationMultibancoDisplayPreferenceValueOn  PaymentMethodConfigurationMultibancoDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationNaverPayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationNaverPayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationNaverPayDisplayPreferencePreferenceNone PaymentMethodConfigurationNaverPayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationNaverPayDisplayPreferencePreferenceOff  PaymentMethodConfigurationNaverPayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationNaverPayDisplayPreferencePreferenceOn   PaymentMethodConfigurationNaverPayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationNaverPayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationNaverPayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationNaverPayDisplayPreferenceValueOff PaymentMethodConfigurationNaverPayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationNaverPayDisplayPreferenceValueOn  PaymentMethodConfigurationNaverPayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationNzBankAccountDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationNzBankAccountDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationNzBankAccountDisplayPreferencePreferenceNone PaymentMethodConfigurationNzBankAccountDisplayPreferencePreference = "none"
	PaymentMethodConfigurationNzBankAccountDisplayPreferencePreferenceOff  PaymentMethodConfigurationNzBankAccountDisplayPreferencePreference = "off"
	PaymentMethodConfigurationNzBankAccountDisplayPreferencePreferenceOn   PaymentMethodConfigurationNzBankAccountDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationNzBankAccountDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationNzBankAccountDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationNzBankAccountDisplayPreferenceValueOff PaymentMethodConfigurationNzBankAccountDisplayPreferenceValue = "off"
	PaymentMethodConfigurationNzBankAccountDisplayPreferenceValueOn  PaymentMethodConfigurationNzBankAccountDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationOXXODisplayPreferencePreference string

// List of values that PaymentMethodConfigurationOXXODisplayPreferencePreference can take
const (
	PaymentMethodConfigurationOXXODisplayPreferencePreferenceNone PaymentMethodConfigurationOXXODisplayPreferencePreference = "none"
	PaymentMethodConfigurationOXXODisplayPreferencePreferenceOff  PaymentMethodConfigurationOXXODisplayPreferencePreference = "off"
	PaymentMethodConfigurationOXXODisplayPreferencePreferenceOn   PaymentMethodConfigurationOXXODisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationOXXODisplayPreferenceValue string

// List of values that PaymentMethodConfigurationOXXODisplayPreferenceValue can take
const (
	PaymentMethodConfigurationOXXODisplayPreferenceValueOff PaymentMethodConfigurationOXXODisplayPreferenceValue = "off"
	PaymentMethodConfigurationOXXODisplayPreferenceValueOn  PaymentMethodConfigurationOXXODisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationP24DisplayPreferencePreference string

// List of values that PaymentMethodConfigurationP24DisplayPreferencePreference can take
const (
	PaymentMethodConfigurationP24DisplayPreferencePreferenceNone PaymentMethodConfigurationP24DisplayPreferencePreference = "none"
	PaymentMethodConfigurationP24DisplayPreferencePreferenceOff  PaymentMethodConfigurationP24DisplayPreferencePreference = "off"
	PaymentMethodConfigurationP24DisplayPreferencePreferenceOn   PaymentMethodConfigurationP24DisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationP24DisplayPreferenceValue string

// List of values that PaymentMethodConfigurationP24DisplayPreferenceValue can take
const (
	PaymentMethodConfigurationP24DisplayPreferenceValueOff PaymentMethodConfigurationP24DisplayPreferenceValue = "off"
	PaymentMethodConfigurationP24DisplayPreferenceValueOn  PaymentMethodConfigurationP24DisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationPayByBankDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationPayByBankDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationPayByBankDisplayPreferencePreferenceNone PaymentMethodConfigurationPayByBankDisplayPreferencePreference = "none"
	PaymentMethodConfigurationPayByBankDisplayPreferencePreferenceOff  PaymentMethodConfigurationPayByBankDisplayPreferencePreference = "off"
	PaymentMethodConfigurationPayByBankDisplayPreferencePreferenceOn   PaymentMethodConfigurationPayByBankDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationPayByBankDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationPayByBankDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationPayByBankDisplayPreferenceValueOff PaymentMethodConfigurationPayByBankDisplayPreferenceValue = "off"
	PaymentMethodConfigurationPayByBankDisplayPreferenceValueOn  PaymentMethodConfigurationPayByBankDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationPaycoDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationPaycoDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationPaycoDisplayPreferencePreferenceNone PaymentMethodConfigurationPaycoDisplayPreferencePreference = "none"
	PaymentMethodConfigurationPaycoDisplayPreferencePreferenceOff  PaymentMethodConfigurationPaycoDisplayPreferencePreference = "off"
	PaymentMethodConfigurationPaycoDisplayPreferencePreferenceOn   PaymentMethodConfigurationPaycoDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationPaycoDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationPaycoDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationPaycoDisplayPreferenceValueOff PaymentMethodConfigurationPaycoDisplayPreferenceValue = "off"
	PaymentMethodConfigurationPaycoDisplayPreferenceValueOn  PaymentMethodConfigurationPaycoDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationPayNowDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationPayNowDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationPayNowDisplayPreferencePreferenceNone PaymentMethodConfigurationPayNowDisplayPreferencePreference = "none"
	PaymentMethodConfigurationPayNowDisplayPreferencePreferenceOff  PaymentMethodConfigurationPayNowDisplayPreferencePreference = "off"
	PaymentMethodConfigurationPayNowDisplayPreferencePreferenceOn   PaymentMethodConfigurationPayNowDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationPayNowDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationPayNowDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationPayNowDisplayPreferenceValueOff PaymentMethodConfigurationPayNowDisplayPreferenceValue = "off"
	PaymentMethodConfigurationPayNowDisplayPreferenceValueOn  PaymentMethodConfigurationPayNowDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationPaypalDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationPaypalDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationPaypalDisplayPreferencePreferenceNone PaymentMethodConfigurationPaypalDisplayPreferencePreference = "none"
	PaymentMethodConfigurationPaypalDisplayPreferencePreferenceOff  PaymentMethodConfigurationPaypalDisplayPreferencePreference = "off"
	PaymentMethodConfigurationPaypalDisplayPreferencePreferenceOn   PaymentMethodConfigurationPaypalDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationPaypalDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationPaypalDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationPaypalDisplayPreferenceValueOff PaymentMethodConfigurationPaypalDisplayPreferenceValue = "off"
	PaymentMethodConfigurationPaypalDisplayPreferenceValueOn  PaymentMethodConfigurationPaypalDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationPixDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationPixDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationPixDisplayPreferencePreferenceNone PaymentMethodConfigurationPixDisplayPreferencePreference = "none"
	PaymentMethodConfigurationPixDisplayPreferencePreferenceOff  PaymentMethodConfigurationPixDisplayPreferencePreference = "off"
	PaymentMethodConfigurationPixDisplayPreferencePreferenceOn   PaymentMethodConfigurationPixDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationPixDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationPixDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationPixDisplayPreferenceValueOff PaymentMethodConfigurationPixDisplayPreferenceValue = "off"
	PaymentMethodConfigurationPixDisplayPreferenceValueOn  PaymentMethodConfigurationPixDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationPromptPayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationPromptPayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationPromptPayDisplayPreferencePreferenceNone PaymentMethodConfigurationPromptPayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationPromptPayDisplayPreferencePreferenceOff  PaymentMethodConfigurationPromptPayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationPromptPayDisplayPreferencePreferenceOn   PaymentMethodConfigurationPromptPayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationPromptPayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationPromptPayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationPromptPayDisplayPreferenceValueOff PaymentMethodConfigurationPromptPayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationPromptPayDisplayPreferenceValueOn  PaymentMethodConfigurationPromptPayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationRevolutPayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationRevolutPayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationRevolutPayDisplayPreferencePreferenceNone PaymentMethodConfigurationRevolutPayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationRevolutPayDisplayPreferencePreferenceOff  PaymentMethodConfigurationRevolutPayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationRevolutPayDisplayPreferencePreferenceOn   PaymentMethodConfigurationRevolutPayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationRevolutPayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationRevolutPayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationRevolutPayDisplayPreferenceValueOff PaymentMethodConfigurationRevolutPayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationRevolutPayDisplayPreferenceValueOn  PaymentMethodConfigurationRevolutPayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationSamsungPayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationSamsungPayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationSamsungPayDisplayPreferencePreferenceNone PaymentMethodConfigurationSamsungPayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationSamsungPayDisplayPreferencePreferenceOff  PaymentMethodConfigurationSamsungPayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationSamsungPayDisplayPreferencePreferenceOn   PaymentMethodConfigurationSamsungPayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationSamsungPayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationSamsungPayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationSamsungPayDisplayPreferenceValueOff PaymentMethodConfigurationSamsungPayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationSamsungPayDisplayPreferenceValueOn  PaymentMethodConfigurationSamsungPayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationSatispayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationSatispayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationSatispayDisplayPreferencePreferenceNone PaymentMethodConfigurationSatispayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationSatispayDisplayPreferencePreferenceOff  PaymentMethodConfigurationSatispayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationSatispayDisplayPreferencePreferenceOn   PaymentMethodConfigurationSatispayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationSatispayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationSatispayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationSatispayDisplayPreferenceValueOff PaymentMethodConfigurationSatispayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationSatispayDisplayPreferenceValueOn  PaymentMethodConfigurationSatispayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationSEPADebitDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationSEPADebitDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationSEPADebitDisplayPreferencePreferenceNone PaymentMethodConfigurationSEPADebitDisplayPreferencePreference = "none"
	PaymentMethodConfigurationSEPADebitDisplayPreferencePreferenceOff  PaymentMethodConfigurationSEPADebitDisplayPreferencePreference = "off"
	PaymentMethodConfigurationSEPADebitDisplayPreferencePreferenceOn   PaymentMethodConfigurationSEPADebitDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationSEPADebitDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationSEPADebitDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationSEPADebitDisplayPreferenceValueOff PaymentMethodConfigurationSEPADebitDisplayPreferenceValue = "off"
	PaymentMethodConfigurationSEPADebitDisplayPreferenceValueOn  PaymentMethodConfigurationSEPADebitDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationSofortDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationSofortDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationSofortDisplayPreferencePreferenceNone PaymentMethodConfigurationSofortDisplayPreferencePreference = "none"
	PaymentMethodConfigurationSofortDisplayPreferencePreferenceOff  PaymentMethodConfigurationSofortDisplayPreferencePreference = "off"
	PaymentMethodConfigurationSofortDisplayPreferencePreferenceOn   PaymentMethodConfigurationSofortDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationSofortDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationSofortDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationSofortDisplayPreferenceValueOff PaymentMethodConfigurationSofortDisplayPreferenceValue = "off"
	PaymentMethodConfigurationSofortDisplayPreferenceValueOn  PaymentMethodConfigurationSofortDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationSwishDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationSwishDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationSwishDisplayPreferencePreferenceNone PaymentMethodConfigurationSwishDisplayPreferencePreference = "none"
	PaymentMethodConfigurationSwishDisplayPreferencePreferenceOff  PaymentMethodConfigurationSwishDisplayPreferencePreference = "off"
	PaymentMethodConfigurationSwishDisplayPreferencePreferenceOn   PaymentMethodConfigurationSwishDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationSwishDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationSwishDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationSwishDisplayPreferenceValueOff PaymentMethodConfigurationSwishDisplayPreferenceValue = "off"
	PaymentMethodConfigurationSwishDisplayPreferenceValueOn  PaymentMethodConfigurationSwishDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationTWINTDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationTWINTDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationTWINTDisplayPreferencePreferenceNone PaymentMethodConfigurationTWINTDisplayPreferencePreference = "none"
	PaymentMethodConfigurationTWINTDisplayPreferencePreferenceOff  PaymentMethodConfigurationTWINTDisplayPreferencePreference = "off"
	PaymentMethodConfigurationTWINTDisplayPreferencePreferenceOn   PaymentMethodConfigurationTWINTDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationTWINTDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationTWINTDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationTWINTDisplayPreferenceValueOff PaymentMethodConfigurationTWINTDisplayPreferenceValue = "off"
	PaymentMethodConfigurationTWINTDisplayPreferenceValueOn  PaymentMethodConfigurationTWINTDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationUSBankAccountDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationUSBankAccountDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationUSBankAccountDisplayPreferencePreferenceNone PaymentMethodConfigurationUSBankAccountDisplayPreferencePreference = "none"
	PaymentMethodConfigurationUSBankAccountDisplayPreferencePreferenceOff  PaymentMethodConfigurationUSBankAccountDisplayPreferencePreference = "off"
	PaymentMethodConfigurationUSBankAccountDisplayPreferencePreferenceOn   PaymentMethodConfigurationUSBankAccountDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationUSBankAccountDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationUSBankAccountDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationUSBankAccountDisplayPreferenceValueOff PaymentMethodConfigurationUSBankAccountDisplayPreferenceValue = "off"
	PaymentMethodConfigurationUSBankAccountDisplayPreferenceValueOn  PaymentMethodConfigurationUSBankAccountDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationWeChatPayDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationWeChatPayDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationWeChatPayDisplayPreferencePreferenceNone PaymentMethodConfigurationWeChatPayDisplayPreferencePreference = "none"
	PaymentMethodConfigurationWeChatPayDisplayPreferencePreferenceOff  PaymentMethodConfigurationWeChatPayDisplayPreferencePreference = "off"
	PaymentMethodConfigurationWeChatPayDisplayPreferencePreferenceOn   PaymentMethodConfigurationWeChatPayDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationWeChatPayDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationWeChatPayDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationWeChatPayDisplayPreferenceValueOff PaymentMethodConfigurationWeChatPayDisplayPreferenceValue = "off"
	PaymentMethodConfigurationWeChatPayDisplayPreferenceValueOn  PaymentMethodConfigurationWeChatPayDisplayPreferenceValue = "on"
)

// The account's display preference.
type PaymentMethodConfigurationZipDisplayPreferencePreference string

// List of values that PaymentMethodConfigurationZipDisplayPreferencePreference can take
const (
	PaymentMethodConfigurationZipDisplayPreferencePreferenceNone PaymentMethodConfigurationZipDisplayPreferencePreference = "none"
	PaymentMethodConfigurationZipDisplayPreferencePreferenceOff  PaymentMethodConfigurationZipDisplayPreferencePreference = "off"
	PaymentMethodConfigurationZipDisplayPreferencePreferenceOn   PaymentMethodConfigurationZipDisplayPreferencePreference = "on"
)

// The effective display preference value.
type PaymentMethodConfigurationZipDisplayPreferenceValue string

// List of values that PaymentMethodConfigurationZipDisplayPreferenceValue can take
const (
	PaymentMethodConfigurationZipDisplayPreferenceValueOff PaymentMethodConfigurationZipDisplayPreferenceValue = "off"
	PaymentMethodConfigurationZipDisplayPreferenceValueOn  PaymentMethodConfigurationZipDisplayPreferenceValue = "on"
)

// List payment method configurations
type PaymentMethodConfigurationListParams struct {
	ListParams `form:"*"`
	// The Connect application to filter by.
	Application *string `form:"application"`
	// Specifies which fields in the response should be expanded.
	Expand []*string `form:"expand"`
}

// AddExpand appends a new field to expand.
func (p *PaymentMethodConfigurationListParams) AddExpand(f string) {
	p.Expand = append(p.Expand, &f)
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationACSSDebitDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Canadian pre-authorized debit payments, check this [page](https://stripe.com/docs/payments/acss-debit) for more details like country availability.
type PaymentMethodConfigurationACSSDebitParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationACSSDebitDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationAffirmDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// [Affirm](https://www.affirm.com/) gives your customers a way to split purchases over a series of payments. Depending on the purchase, they can pay with four interest-free payments (Split Pay) or pay over a longer term (Installments), which might include interest. Check this [page](https://stripe.com/docs/payments/affirm) for more details like country availability.
type PaymentMethodConfigurationAffirmParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationAffirmDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationAfterpayClearpayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Afterpay gives your customers a way to pay for purchases in installments, check this [page](https://stripe.com/docs/payments/afterpay-clearpay) for more details like country availability. Afterpay is particularly popular among businesses selling fashion, beauty, and sports products.
type PaymentMethodConfigurationAfterpayClearpayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationAfterpayClearpayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationAlipayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Alipay is a digital wallet in China that has more than a billion active users worldwide. Alipay users can pay on the web or on a mobile device using login credentials or their Alipay app. Alipay has a low dispute rate and reduces fraud by authenticating payments using the customer's login credentials. Check this [page](https://stripe.com/docs/payments/alipay) for more details.
type PaymentMethodConfigurationAlipayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationAlipayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationAlmaDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Alma is a Buy Now, Pay Later payment method that offers customers the ability to pay in 2, 3, or 4 installments.
type PaymentMethodConfigurationAlmaParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationAlmaDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationAmazonPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Amazon Pay is a wallet payment method that lets your customers check out the same way as on Amazon.
type PaymentMethodConfigurationAmazonPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationAmazonPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationApplePayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users can accept [Apple Pay](https://stripe.com/payments/apple-pay) in iOS applications in iOS 9 and later, and on the web in Safari starting with iOS 10 or macOS Sierra. There are no additional fees to process Apple Pay payments, and the [pricing](https://stripe.com/pricing) is the same as other card transactions. Check this [page](https://stripe.com/docs/apple-pay) for more details.
type PaymentMethodConfigurationApplePayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationApplePayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationApplePayLaterDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Apple Pay Later, a payment method for customers to buy now and pay later, gives your customers a way to split purchases into four installments across six weeks.
type PaymentMethodConfigurationApplePayLaterParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationApplePayLaterDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationAUBECSDebitDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in Australia can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with an Australian bank account. Check this [page](https://stripe.com/docs/payments/au-becs-debit) for more details.
type PaymentMethodConfigurationAUBECSDebitParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationAUBECSDebitDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationBACSDebitDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in the UK can accept Bacs Direct Debit payments from customers with a UK bank account, check this [page](https://stripe.com/docs/payments/payment-methods/bacs-debit) for more details.
type PaymentMethodConfigurationBACSDebitParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationBACSDebitDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationBancontactDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Bancontact is the most popular online payment method in Belgium, with over 15 million cards in circulation. [Customers](https://stripe.com/docs/api/customers) use a Bancontact card or mobile app linked to a Belgian bank account to make online payments that are secure, guaranteed, and confirmed immediately. Check this [page](https://stripe.com/docs/payments/bancontact) for more details.
type PaymentMethodConfigurationBancontactParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationBancontactDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationBillieDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Billie is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method that offers businesses Pay by Invoice where they offer payment terms ranging from 7-120 days. Customers are redirected from your website or app, authorize the payment with Billie, then return to your website or app. You get [immediate notification](https://docs.stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
type PaymentMethodConfigurationBillieParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationBillieDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationBLIKDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// BLIK is a [single use](https://stripe.com/docs/payments/payment-methods#usage) payment method that requires customers to authenticate their payments. When customers want to pay online using BLIK, they request a six-digit code from their banking application and enter it into the payment collection form. Check this [page](https://stripe.com/docs/payments/blik) for more details.
type PaymentMethodConfigurationBLIKParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationBLIKDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationBoletoDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Boleto is an official (regulated by the Central Bank of Brazil) payment method in Brazil. Check this [page](https://stripe.com/docs/payments/boleto) for more details.
type PaymentMethodConfigurationBoletoParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationBoletoDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCardDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Cards are a popular way for consumers and businesses to pay online or in person. Stripe supports global and local card networks.
type PaymentMethodConfigurationCardParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCardDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCartesBancairesDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Cartes Bancaires is France's local card network. More than 95% of these cards are co-branded with either Visa or Mastercard, meaning you can process these cards over either Cartes Bancaires or the Visa or Mastercard networks. Check this [page](https://stripe.com/docs/payments/cartes-bancaires) for more details.
type PaymentMethodConfigurationCartesBancairesParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCartesBancairesDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCashAppDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Cash App is a popular consumer app in the US that allows customers to bank, invest, send, and receive money using their digital wallet. Check this [page](https://stripe.com/docs/payments/cash-app-pay) for more details.
type PaymentMethodConfigurationCashAppParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCashAppDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCustomerBalanceDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Uses a customer's [cash balance](https://stripe.com/docs/payments/customer-balance) for the payment. The cash balance can be funded via a bank transfer. Check this [page](https://stripe.com/docs/payments/bank-transfers) for more details.
type PaymentMethodConfigurationCustomerBalanceParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCustomerBalanceDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationEPSDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// EPS is an Austria-based payment method that allows customers to complete transactions online using their bank credentials. EPS is supported by all Austrian banks and is accepted by over 80% of Austrian online retailers. Check this [page](https://stripe.com/docs/payments/eps) for more details.
type PaymentMethodConfigurationEPSParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationEPSDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationFPXDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Financial Process Exchange (FPX) is a Malaysia-based payment method that allows customers to complete transactions online using their bank credentials. Bank Negara Malaysia (BNM), the Central Bank of Malaysia, and eleven other major Malaysian financial institutions are members of the PayNet Group, which owns and operates FPX. It is one of the most popular online payment methods in Malaysia, with nearly 90 million transactions in 2018 according to BNM. Check this [page](https://stripe.com/docs/payments/fpx) for more details.
type PaymentMethodConfigurationFPXParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationFPXDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationGiropayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// giropay is a German payment method based on online banking, introduced in 2006. It allows customers to complete transactions online using their online banking environment, with funds debited from their bank account. Depending on their bank, customers confirm payments on giropay using a second factor of authentication or a PIN. giropay accounts for 10% of online checkouts in Germany. Check this [page](https://stripe.com/docs/payments/giropay) for more details.
type PaymentMethodConfigurationGiropayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationGiropayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationGooglePayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Google Pay allows customers to make payments in your app or website using any credit or debit card saved to their Google Account, including those from Google Play, YouTube, Chrome, or an Android device. Use the Google Pay API to request any credit or debit card stored in your customer's Google account. Check this [page](https://stripe.com/docs/google-pay) for more details.
type PaymentMethodConfigurationGooglePayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationGooglePayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationGrabpayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// GrabPay is a payment method developed by [Grab](https://www.grab.com/sg/consumer/finance/pay/). GrabPay is a digital wallet - customers maintain a balance in their wallets that they pay out with. Check this [page](https://stripe.com/docs/payments/grabpay) for more details.
type PaymentMethodConfigurationGrabpayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationGrabpayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationIDEALDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// iDEAL is a Netherlands-based payment method that allows customers to complete transactions online using their bank credentials. All major Dutch banks are members of Currence, the scheme that operates iDEAL, making it the most popular online payment method in the Netherlands with a share of online transactions close to 55%. Check this [page](https://stripe.com/docs/payments/ideal) for more details.
type PaymentMethodConfigurationIDEALParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationIDEALDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationJCBDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// JCB is a credit card company based in Japan. JCB is currently available in Japan to businesses approved by JCB, and available to all businesses in Australia, Canada, Hong Kong, Japan, New Zealand, Singapore, Switzerland, United Kingdom, United States, and all countries in the European Economic Area except Iceland. Check this [page](https://support.stripe.com/questions/accepting-japan-credit-bureau-%28jcb%29-payments) for more details.
type PaymentMethodConfigurationJCBParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationJCBDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationKakaoPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Kakao Pay is a popular local wallet available in South Korea.
type PaymentMethodConfigurationKakaoPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationKakaoPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationKlarnaDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Klarna gives customers a range of [payment options](https://stripe.com/docs/payments/klarna#payment-options) during checkout. Available payment options vary depending on the customer's billing address and the transaction amount. These payment options make it convenient for customers to purchase items in all price ranges. Check this [page](https://stripe.com/docs/payments/klarna) for more details.
type PaymentMethodConfigurationKlarnaParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationKlarnaDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationKonbiniDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Konbini allows customers in Japan to pay for bills and online purchases at convenience stores with cash. Check this [page](https://stripe.com/docs/payments/konbini) for more details.
type PaymentMethodConfigurationKonbiniParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationKonbiniDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationKrCardDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Korean cards let users pay using locally issued cards from South Korea.
type PaymentMethodConfigurationKrCardParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationKrCardDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationLinkDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// [Link](https://stripe.com/docs/payments/link) is a payment method network. With Link, users save their payment details once, then reuse that information to pay with one click for any business on the network.
type PaymentMethodConfigurationLinkParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationLinkDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationMobilepayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// MobilePay is a [single-use](https://stripe.com/docs/payments/payment-methods#usage) card wallet payment method used in Denmark and Finland. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the MobilePay app. Check this [page](https://stripe.com/docs/payments/mobilepay) for more details.
type PaymentMethodConfigurationMobilepayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationMobilepayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationMultibancoDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in Europe and the United States can accept Multibanco payments from customers in Portugal using [Sources](https://stripe.com/docs/sources)—a single integration path for creating payments using any supported method.
type PaymentMethodConfigurationMultibancoParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationMultibancoDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationNaverPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Naver Pay is a popular local wallet available in South Korea.
type PaymentMethodConfigurationNaverPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationNaverPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationNzBankAccountDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in New Zealand can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with a New Zeland bank account. Check this [page](https://stripe.com/docs/payments/nz-bank-account) for more details.
type PaymentMethodConfigurationNzBankAccountParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationNzBankAccountDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationOXXODisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// OXXO is a Mexican chain of convenience stores with thousands of locations across Latin America and represents nearly 20% of online transactions in Mexico. OXXO allows customers to pay bills and online purchases in-store with cash. Check this [page](https://stripe.com/docs/payments/oxxo) for more details.
type PaymentMethodConfigurationOXXOParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationOXXODisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationP24DisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Przelewy24 is a Poland-based payment method aggregator that allows customers to complete transactions online using bank transfers and other methods. Bank transfers account for 30% of online payments in Poland and Przelewy24 provides a way for customers to pay with over 165 banks. Check this [page](https://stripe.com/docs/payments/p24) for more details.
type PaymentMethodConfigurationP24Params struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationP24DisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationPayByBankDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Pay by bank is a redirect payment method backed by bank transfers. A customer is redirected to their bank to authorize a bank transfer for a given amount. This removes a lot of the error risks inherent in waiting for the customer to initiate a transfer themselves, and is less expensive than card payments.
type PaymentMethodConfigurationPayByBankParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationPayByBankDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationPaycoDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// PAYCO is a [single-use](https://docs.stripe.com/payments/payment-methods#usage local wallet available in South Korea.
type PaymentMethodConfigurationPaycoParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationPaycoDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationPayNowDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// PayNow is a Singapore-based payment method that allows customers to make a payment using their preferred app from participating banks and participating non-bank financial institutions. Check this [page](https://stripe.com/docs/payments/paynow) for more details.
type PaymentMethodConfigurationPayNowParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationPayNowDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationPaypalDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// PayPal, a digital wallet popular with customers in Europe, allows your customers worldwide to pay using their PayPal account. Check this [page](https://stripe.com/docs/payments/paypal) for more details.
type PaymentMethodConfigurationPaypalParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationPaypalDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationPixDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Pix is a payment method popular in Brazil. When paying with Pix, customers authenticate and approve payments by scanning a QR code in their preferred banking app. Check this [page](https://docs.stripe.com/payments/pix) for more details.
type PaymentMethodConfigurationPixParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationPixDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationPromptPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// PromptPay is a Thailand-based payment method that allows customers to make a payment using their preferred app from participating banks. Check this [page](https://stripe.com/docs/payments/promptpay) for more details.
type PaymentMethodConfigurationPromptPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationPromptPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationRevolutPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Revolut Pay, developed by Revolut, a global finance app, is a digital wallet payment method. Revolut Pay uses the customer's stored balance or cards to fund the payment, and offers the option for non-Revolut customers to save their details after their first purchase.
type PaymentMethodConfigurationRevolutPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationRevolutPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationSamsungPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Samsung Pay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage local wallet available in South Korea.
type PaymentMethodConfigurationSamsungPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationSamsungPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationSatispayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Satispay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method where customers are required to [authenticate](https://docs.stripe.com/payments/payment-methods#customer-actions) their payment. Customers pay by being redirected from your website or app, authorizing the payment with Satispay, then returning to your website or app. You get [immediate notification](https://docs.stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
type PaymentMethodConfigurationSatispayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationSatispayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationSEPADebitDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// The [Single Euro Payments Area (SEPA)](https://en.wikipedia.org/wiki/Single_Euro_Payments_Area) is an initiative of the European Union to simplify payments within and across member countries. SEPA established and enforced banking standards to allow for the direct debiting of every EUR-denominated bank account within the SEPA region, check this [page](https://stripe.com/docs/payments/sepa-debit) for more details.
type PaymentMethodConfigurationSEPADebitParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationSEPADebitDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationSofortDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in Europe and the United States can use the [Payment Intents API](https://stripe.com/docs/payments/payment-intents)—a single integration path for creating payments using any supported method—to accept [Sofort](https://www.sofort.com/) payments from customers. Check this [page](https://stripe.com/docs/payments/sofort) for more details.
type PaymentMethodConfigurationSofortParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationSofortDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationSwishDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Swish is a [real-time](https://stripe.com/docs/payments/real-time) payment method popular in Sweden. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the Swish mobile app and the Swedish BankID mobile app. Check this [page](https://stripe.com/docs/payments/swish) for more details.
type PaymentMethodConfigurationSwishParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationSwishDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationTWINTDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Twint is a payment method popular in Switzerland. It allows customers to pay using their mobile phone. Check this [page](https://docs.stripe.com/payments/twint) for more details.
type PaymentMethodConfigurationTWINTParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationTWINTDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUSBankAccountDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in the United States can accept ACH direct debit payments from customers with a US bank account using the Automated Clearing House (ACH) payments system operated by Nacha. Check this [page](https://stripe.com/docs/payments/ach-direct-debit) for more details.
type PaymentMethodConfigurationUSBankAccountParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUSBankAccountDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationWeChatPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// WeChat, owned by Tencent, is China's leading mobile app with over 1 billion monthly active users. Chinese consumers can use WeChat Pay to pay for goods and services inside of businesses' apps and websites. WeChat Pay users buy most frequently in gaming, e-commerce, travel, online education, and food/nutrition. Check this [page](https://stripe.com/docs/payments/wechat-pay) for more details.
type PaymentMethodConfigurationWeChatPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationWeChatPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationZipDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Zip gives your customers a way to split purchases over a series of payments. Check this [page](https://stripe.com/docs/payments/zip) for more details like country availability.
type PaymentMethodConfigurationZipParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationZipDisplayPreferenceParams `form:"display_preference"`
}

// Creates a payment method configuration
type PaymentMethodConfigurationParams struct {
	Params `form:"*"`
	// Canadian pre-authorized debit payments, check this [page](https://stripe.com/docs/payments/acss-debit) for more details like country availability.
	ACSSDebit *PaymentMethodConfigurationACSSDebitParams `form:"acss_debit"`
	// Whether the configuration can be used for new payments.
	Active *bool `form:"active"`
	// [Affirm](https://www.affirm.com/) gives your customers a way to split purchases over a series of payments. Depending on the purchase, they can pay with four interest-free payments (Split Pay) or pay over a longer term (Installments), which might include interest. Check this [page](https://stripe.com/docs/payments/affirm) for more details like country availability.
	Affirm *PaymentMethodConfigurationAffirmParams `form:"affirm"`
	// Afterpay gives your customers a way to pay for purchases in installments, check this [page](https://stripe.com/docs/payments/afterpay-clearpay) for more details like country availability. Afterpay is particularly popular among businesses selling fashion, beauty, and sports products.
	AfterpayClearpay *PaymentMethodConfigurationAfterpayClearpayParams `form:"afterpay_clearpay"`
	// Alipay is a digital wallet in China that has more than a billion active users worldwide. Alipay users can pay on the web or on a mobile device using login credentials or their Alipay app. Alipay has a low dispute rate and reduces fraud by authenticating payments using the customer's login credentials. Check this [page](https://stripe.com/docs/payments/alipay) for more details.
	Alipay *PaymentMethodConfigurationAlipayParams `form:"alipay"`
	// Alma is a Buy Now, Pay Later payment method that offers customers the ability to pay in 2, 3, or 4 installments.
	Alma *PaymentMethodConfigurationAlmaParams `form:"alma"`
	// Amazon Pay is a wallet payment method that lets your customers check out the same way as on Amazon.
	AmazonPay *PaymentMethodConfigurationAmazonPayParams `form:"amazon_pay"`
	// Stripe users can accept [Apple Pay](https://stripe.com/payments/apple-pay) in iOS applications in iOS 9 and later, and on the web in Safari starting with iOS 10 or macOS Sierra. There are no additional fees to process Apple Pay payments, and the [pricing](https://stripe.com/pricing) is the same as other card transactions. Check this [page](https://stripe.com/docs/apple-pay) for more details.
	ApplePay *PaymentMethodConfigurationApplePayParams `form:"apple_pay"`
	// Apple Pay Later, a payment method for customers to buy now and pay later, gives your customers a way to split purchases into four installments across six weeks.
	ApplePayLater *PaymentMethodConfigurationApplePayLaterParams `form:"apple_pay_later"`
	// Stripe users in Australia can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with an Australian bank account. Check this [page](https://stripe.com/docs/payments/au-becs-debit) for more details.
	AUBECSDebit *PaymentMethodConfigurationAUBECSDebitParams `form:"au_becs_debit"`
	// Stripe users in the UK can accept Bacs Direct Debit payments from customers with a UK bank account, check this [page](https://stripe.com/docs/payments/payment-methods/bacs-debit) for more details.
	BACSDebit *PaymentMethodConfigurationBACSDebitParams `form:"bacs_debit"`
	// Bancontact is the most popular online payment method in Belgium, with over 15 million cards in circulation. [Customers](https://stripe.com/docs/api/customers) use a Bancontact card or mobile app linked to a Belgian bank account to make online payments that are secure, guaranteed, and confirmed immediately. Check this [page](https://stripe.com/docs/payments/bancontact) for more details.
	Bancontact *PaymentMethodConfigurationBancontactParams `form:"bancontact"`
	// Billie is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method that offers businesses Pay by Invoice where they offer payment terms ranging from 7-120 days. Customers are redirected from your website or app, authorize the payment with Billie, then return to your website or app. You get [immediate notification](https://docs.stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
	Billie *PaymentMethodConfigurationBillieParams `form:"billie"`
	// BLIK is a [single use](https://stripe.com/docs/payments/payment-methods#usage) payment method that requires customers to authenticate their payments. When customers want to pay online using BLIK, they request a six-digit code from their banking application and enter it into the payment collection form. Check this [page](https://stripe.com/docs/payments/blik) for more details.
	BLIK *PaymentMethodConfigurationBLIKParams `form:"blik"`
	// Boleto is an official (regulated by the Central Bank of Brazil) payment method in Brazil. Check this [page](https://stripe.com/docs/payments/boleto) for more details.
	Boleto *PaymentMethodConfigurationBoletoParams `form:"boleto"`
	// Cards are a popular way for consumers and businesses to pay online or in person. Stripe supports global and local card networks.
	Card *PaymentMethodConfigurationCardParams `form:"card"`
	// Cartes Bancaires is France's local card network. More than 95% of these cards are co-branded with either Visa or Mastercard, meaning you can process these cards over either Cartes Bancaires or the Visa or Mastercard networks. Check this [page](https://stripe.com/docs/payments/cartes-bancaires) for more details.
	CartesBancaires *PaymentMethodConfigurationCartesBancairesParams `form:"cartes_bancaires"`
	// Cash App is a popular consumer app in the US that allows customers to bank, invest, send, and receive money using their digital wallet. Check this [page](https://stripe.com/docs/payments/cash-app-pay) for more details.
	CashApp *PaymentMethodConfigurationCashAppParams `form:"cashapp"`
	// Uses a customer's [cash balance](https://stripe.com/docs/payments/customer-balance) for the payment. The cash balance can be funded via a bank transfer. Check this [page](https://stripe.com/docs/payments/bank-transfers) for more details.
	CustomerBalance *PaymentMethodConfigurationCustomerBalanceParams `form:"customer_balance"`
	// EPS is an Austria-based payment method that allows customers to complete transactions online using their bank credentials. EPS is supported by all Austrian banks and is accepted by over 80% of Austrian online retailers. Check this [page](https://stripe.com/docs/payments/eps) for more details.
	EPS *PaymentMethodConfigurationEPSParams `form:"eps"`
	// Specifies which fields in the response should be expanded.
	Expand []*string `form:"expand"`
	// Financial Process Exchange (FPX) is a Malaysia-based payment method that allows customers to complete transactions online using their bank credentials. Bank Negara Malaysia (BNM), the Central Bank of Malaysia, and eleven other major Malaysian financial institutions are members of the PayNet Group, which owns and operates FPX. It is one of the most popular online payment methods in Malaysia, with nearly 90 million transactions in 2018 according to BNM. Check this [page](https://stripe.com/docs/payments/fpx) for more details.
	FPX *PaymentMethodConfigurationFPXParams `form:"fpx"`
	// giropay is a German payment method based on online banking, introduced in 2006. It allows customers to complete transactions online using their online banking environment, with funds debited from their bank account. Depending on their bank, customers confirm payments on giropay using a second factor of authentication or a PIN. giropay accounts for 10% of online checkouts in Germany. Check this [page](https://stripe.com/docs/payments/giropay) for more details.
	Giropay *PaymentMethodConfigurationGiropayParams `form:"giropay"`
	// Google Pay allows customers to make payments in your app or website using any credit or debit card saved to their Google Account, including those from Google Play, YouTube, Chrome, or an Android device. Use the Google Pay API to request any credit or debit card stored in your customer's Google account. Check this [page](https://stripe.com/docs/google-pay) for more details.
	GooglePay *PaymentMethodConfigurationGooglePayParams `form:"google_pay"`
	// GrabPay is a payment method developed by [Grab](https://www.grab.com/sg/consumer/finance/pay/). GrabPay is a digital wallet - customers maintain a balance in their wallets that they pay out with. Check this [page](https://stripe.com/docs/payments/grabpay) for more details.
	Grabpay *PaymentMethodConfigurationGrabpayParams `form:"grabpay"`
	// iDEAL is a Netherlands-based payment method that allows customers to complete transactions online using their bank credentials. All major Dutch banks are members of Currence, the scheme that operates iDEAL, making it the most popular online payment method in the Netherlands with a share of online transactions close to 55%. Check this [page](https://stripe.com/docs/payments/ideal) for more details.
	IDEAL *PaymentMethodConfigurationIDEALParams `form:"ideal"`
	// JCB is a credit card company based in Japan. JCB is currently available in Japan to businesses approved by JCB, and available to all businesses in Australia, Canada, Hong Kong, Japan, New Zealand, Singapore, Switzerland, United Kingdom, United States, and all countries in the European Economic Area except Iceland. Check this [page](https://support.stripe.com/questions/accepting-japan-credit-bureau-%28jcb%29-payments) for more details.
	JCB *PaymentMethodConfigurationJCBParams `form:"jcb"`
	// Kakao Pay is a popular local wallet available in South Korea.
	KakaoPay *PaymentMethodConfigurationKakaoPayParams `form:"kakao_pay"`
	// Klarna gives customers a range of [payment options](https://stripe.com/docs/payments/klarna#payment-options) during checkout. Available payment options vary depending on the customer's billing address and the transaction amount. These payment options make it convenient for customers to purchase items in all price ranges. Check this [page](https://stripe.com/docs/payments/klarna) for more details.
	Klarna *PaymentMethodConfigurationKlarnaParams `form:"klarna"`
	// Konbini allows customers in Japan to pay for bills and online purchases at convenience stores with cash. Check this [page](https://stripe.com/docs/payments/konbini) for more details.
	Konbini *PaymentMethodConfigurationKonbiniParams `form:"konbini"`
	// Korean cards let users pay using locally issued cards from South Korea.
	KrCard *PaymentMethodConfigurationKrCardParams `form:"kr_card"`
	// [Link](https://stripe.com/docs/payments/link) is a payment method network. With Link, users save their payment details once, then reuse that information to pay with one click for any business on the network.
	Link *PaymentMethodConfigurationLinkParams `form:"link"`
	// MobilePay is a [single-use](https://stripe.com/docs/payments/payment-methods#usage) card wallet payment method used in Denmark and Finland. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the MobilePay app. Check this [page](https://stripe.com/docs/payments/mobilepay) for more details.
	Mobilepay *PaymentMethodConfigurationMobilepayParams `form:"mobilepay"`
	// Stripe users in Europe and the United States can accept Multibanco payments from customers in Portugal using [Sources](https://stripe.com/docs/sources)—a single integration path for creating payments using any supported method.
	Multibanco *PaymentMethodConfigurationMultibancoParams `form:"multibanco"`
	// Configuration name.
	Name *string `form:"name"`
	// Naver Pay is a popular local wallet available in South Korea.
	NaverPay *PaymentMethodConfigurationNaverPayParams `form:"naver_pay"`
	// Stripe users in New Zealand can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with a New Zeland bank account. Check this [page](https://stripe.com/docs/payments/nz-bank-account) for more details.
	NzBankAccount *PaymentMethodConfigurationNzBankAccountParams `form:"nz_bank_account"`
	// OXXO is a Mexican chain of convenience stores with thousands of locations across Latin America and represents nearly 20% of online transactions in Mexico. OXXO allows customers to pay bills and online purchases in-store with cash. Check this [page](https://stripe.com/docs/payments/oxxo) for more details.
	OXXO *PaymentMethodConfigurationOXXOParams `form:"oxxo"`
	// Przelewy24 is a Poland-based payment method aggregator that allows customers to complete transactions online using bank transfers and other methods. Bank transfers account for 30% of online payments in Poland and Przelewy24 provides a way for customers to pay with over 165 banks. Check this [page](https://stripe.com/docs/payments/p24) for more details.
	P24 *PaymentMethodConfigurationP24Params `form:"p24"`
	// Configuration's parent configuration. Specify to create a child configuration.
	Parent *string `form:"parent"`
	// Pay by bank is a redirect payment method backed by bank transfers. A customer is redirected to their bank to authorize a bank transfer for a given amount. This removes a lot of the error risks inherent in waiting for the customer to initiate a transfer themselves, and is less expensive than card payments.
	PayByBank *PaymentMethodConfigurationPayByBankParams `form:"pay_by_bank"`
	// PAYCO is a [single-use](https://docs.stripe.com/payments/payment-methods#usage local wallet available in South Korea.
	Payco *PaymentMethodConfigurationPaycoParams `form:"payco"`
	// PayNow is a Singapore-based payment method that allows customers to make a payment using their preferred app from participating banks and participating non-bank financial institutions. Check this [page](https://stripe.com/docs/payments/paynow) for more details.
	PayNow *PaymentMethodConfigurationPayNowParams `form:"paynow"`
	// PayPal, a digital wallet popular with customers in Europe, allows your customers worldwide to pay using their PayPal account. Check this [page](https://stripe.com/docs/payments/paypal) for more details.
	Paypal *PaymentMethodConfigurationPaypalParams `form:"paypal"`
	// Pix is a payment method popular in Brazil. When paying with Pix, customers authenticate and approve payments by scanning a QR code in their preferred banking app. Check this [page](https://docs.stripe.com/payments/pix) for more details.
	Pix *PaymentMethodConfigurationPixParams `form:"pix"`
	// PromptPay is a Thailand-based payment method that allows customers to make a payment using their preferred app from participating banks. Check this [page](https://stripe.com/docs/payments/promptpay) for more details.
	PromptPay *PaymentMethodConfigurationPromptPayParams `form:"promptpay"`
	// Revolut Pay, developed by Revolut, a global finance app, is a digital wallet payment method. Revolut Pay uses the customer's stored balance or cards to fund the payment, and offers the option for non-Revolut customers to save their details after their first purchase.
	RevolutPay *PaymentMethodConfigurationRevolutPayParams `form:"revolut_pay"`
	// Samsung Pay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage local wallet available in South Korea.
	SamsungPay *PaymentMethodConfigurationSamsungPayParams `form:"samsung_pay"`
	// Satispay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method where customers are required to [authenticate](https://docs.stripe.com/payments/payment-methods#customer-actions) their payment. Customers pay by being redirected from your website or app, authorizing the payment with Satispay, then returning to your website or app. You get [immediate notification](https://docs.stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
	Satispay *PaymentMethodConfigurationSatispayParams `form:"satispay"`
	// The [Single Euro Payments Area (SEPA)](https://en.wikipedia.org/wiki/Single_Euro_Payments_Area) is an initiative of the European Union to simplify payments within and across member countries. SEPA established and enforced banking standards to allow for the direct debiting of every EUR-denominated bank account within the SEPA region, check this [page](https://stripe.com/docs/payments/sepa-debit) for more details.
	SEPADebit *PaymentMethodConfigurationSEPADebitParams `form:"sepa_debit"`
	// Stripe users in Europe and the United States can use the [Payment Intents API](https://stripe.com/docs/payments/payment-intents)—a single integration path for creating payments using any supported method—to accept [Sofort](https://www.sofort.com/) payments from customers. Check this [page](https://stripe.com/docs/payments/sofort) for more details.
	Sofort *PaymentMethodConfigurationSofortParams `form:"sofort"`
	// Swish is a [real-time](https://stripe.com/docs/payments/real-time) payment method popular in Sweden. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the Swish mobile app and the Swedish BankID mobile app. Check this [page](https://stripe.com/docs/payments/swish) for more details.
	Swish *PaymentMethodConfigurationSwishParams `form:"swish"`
	// Twint is a payment method popular in Switzerland. It allows customers to pay using their mobile phone. Check this [page](https://docs.stripe.com/payments/twint) for more details.
	TWINT *PaymentMethodConfigurationTWINTParams `form:"twint"`
	// Stripe users in the United States can accept ACH direct debit payments from customers with a US bank account using the Automated Clearing House (ACH) payments system operated by Nacha. Check this [page](https://stripe.com/docs/payments/ach-direct-debit) for more details.
	USBankAccount *PaymentMethodConfigurationUSBankAccountParams `form:"us_bank_account"`
	// WeChat, owned by Tencent, is China's leading mobile app with over 1 billion monthly active users. Chinese consumers can use WeChat Pay to pay for goods and services inside of businesses' apps and websites. WeChat Pay users buy most frequently in gaming, e-commerce, travel, online education, and food/nutrition. Check this [page](https://stripe.com/docs/payments/wechat-pay) for more details.
	WeChatPay *PaymentMethodConfigurationWeChatPayParams `form:"wechat_pay"`
	// Zip gives your customers a way to split purchases over a series of payments. Check this [page](https://stripe.com/docs/payments/zip) for more details like country availability.
	Zip *PaymentMethodConfigurationZipParams `form:"zip"`
}

// AddExpand appends a new field to expand.
func (p *PaymentMethodConfigurationParams) AddExpand(f string) {
	p.Expand = append(p.Expand, &f)
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateACSSDebitDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Canadian pre-authorized debit payments, check this [page](https://stripe.com/docs/payments/acss-debit) for more details like country availability.
type PaymentMethodConfigurationCreateACSSDebitParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateACSSDebitDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateAffirmDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// [Affirm](https://www.affirm.com/) gives your customers a way to split purchases over a series of payments. Depending on the purchase, they can pay with four interest-free payments (Split Pay) or pay over a longer term (Installments), which might include interest. Check this [page](https://stripe.com/docs/payments/affirm) for more details like country availability.
type PaymentMethodConfigurationCreateAffirmParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateAffirmDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateAfterpayClearpayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Afterpay gives your customers a way to pay for purchases in installments, check this [page](https://stripe.com/docs/payments/afterpay-clearpay) for more details like country availability. Afterpay is particularly popular among businesses selling fashion, beauty, and sports products.
type PaymentMethodConfigurationCreateAfterpayClearpayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateAfterpayClearpayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateAlipayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Alipay is a digital wallet in China that has more than a billion active users worldwide. Alipay users can pay on the web or on a mobile device using login credentials or their Alipay app. Alipay has a low dispute rate and reduces fraud by authenticating payments using the customer's login credentials. Check this [page](https://stripe.com/docs/payments/alipay) for more details.
type PaymentMethodConfigurationCreateAlipayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateAlipayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateAlmaDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Alma is a Buy Now, Pay Later payment method that offers customers the ability to pay in 2, 3, or 4 installments.
type PaymentMethodConfigurationCreateAlmaParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateAlmaDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateAmazonPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Amazon Pay is a wallet payment method that lets your customers check out the same way as on Amazon.
type PaymentMethodConfigurationCreateAmazonPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateAmazonPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateApplePayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users can accept [Apple Pay](https://stripe.com/payments/apple-pay) in iOS applications in iOS 9 and later, and on the web in Safari starting with iOS 10 or macOS Sierra. There are no additional fees to process Apple Pay payments, and the [pricing](https://stripe.com/pricing) is the same as other card transactions. Check this [page](https://stripe.com/docs/apple-pay) for more details.
type PaymentMethodConfigurationCreateApplePayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateApplePayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateApplePayLaterDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Apple Pay Later, a payment method for customers to buy now and pay later, gives your customers a way to split purchases into four installments across six weeks.
type PaymentMethodConfigurationCreateApplePayLaterParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateApplePayLaterDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateAUBECSDebitDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in Australia can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with an Australian bank account. Check this [page](https://stripe.com/docs/payments/au-becs-debit) for more details.
type PaymentMethodConfigurationCreateAUBECSDebitParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateAUBECSDebitDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateBACSDebitDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in the UK can accept Bacs Direct Debit payments from customers with a UK bank account, check this [page](https://stripe.com/docs/payments/payment-methods/bacs-debit) for more details.
type PaymentMethodConfigurationCreateBACSDebitParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateBACSDebitDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateBancontactDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Bancontact is the most popular online payment method in Belgium, with over 15 million cards in circulation. [Customers](https://stripe.com/docs/api/customers) use a Bancontact card or mobile app linked to a Belgian bank account to make online payments that are secure, guaranteed, and confirmed immediately. Check this [page](https://stripe.com/docs/payments/bancontact) for more details.
type PaymentMethodConfigurationCreateBancontactParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateBancontactDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateBillieDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Billie is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method that offers businesses Pay by Invoice where they offer payment terms ranging from 7-120 days. Customers are redirected from your website or app, authorize the payment with Billie, then return to your website or app. You get [immediate notification](https://docs.stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
type PaymentMethodConfigurationCreateBillieParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateBillieDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateBLIKDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// BLIK is a [single use](https://stripe.com/docs/payments/payment-methods#usage) payment method that requires customers to authenticate their payments. When customers want to pay online using BLIK, they request a six-digit code from their banking application and enter it into the payment collection form. Check this [page](https://stripe.com/docs/payments/blik) for more details.
type PaymentMethodConfigurationCreateBLIKParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateBLIKDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateBoletoDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Boleto is an official (regulated by the Central Bank of Brazil) payment method in Brazil. Check this [page](https://stripe.com/docs/payments/boleto) for more details.
type PaymentMethodConfigurationCreateBoletoParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateBoletoDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateCardDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Cards are a popular way for consumers and businesses to pay online or in person. Stripe supports global and local card networks.
type PaymentMethodConfigurationCreateCardParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateCardDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateCartesBancairesDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Cartes Bancaires is France's local card network. More than 95% of these cards are co-branded with either Visa or Mastercard, meaning you can process these cards over either Cartes Bancaires or the Visa or Mastercard networks. Check this [page](https://stripe.com/docs/payments/cartes-bancaires) for more details.
type PaymentMethodConfigurationCreateCartesBancairesParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateCartesBancairesDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateCashAppDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Cash App is a popular consumer app in the US that allows customers to bank, invest, send, and receive money using their digital wallet. Check this [page](https://stripe.com/docs/payments/cash-app-pay) for more details.
type PaymentMethodConfigurationCreateCashAppParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateCashAppDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateCustomerBalanceDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Uses a customer's [cash balance](https://stripe.com/docs/payments/customer-balance) for the payment. The cash balance can be funded via a bank transfer. Check this [page](https://stripe.com/docs/payments/bank-transfers) for more details.
type PaymentMethodConfigurationCreateCustomerBalanceParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateCustomerBalanceDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateEPSDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// EPS is an Austria-based payment method that allows customers to complete transactions online using their bank credentials. EPS is supported by all Austrian banks and is accepted by over 80% of Austrian online retailers. Check this [page](https://stripe.com/docs/payments/eps) for more details.
type PaymentMethodConfigurationCreateEPSParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateEPSDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateFPXDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Financial Process Exchange (FPX) is a Malaysia-based payment method that allows customers to complete transactions online using their bank credentials. Bank Negara Malaysia (BNM), the Central Bank of Malaysia, and eleven other major Malaysian financial institutions are members of the PayNet Group, which owns and operates FPX. It is one of the most popular online payment methods in Malaysia, with nearly 90 million transactions in 2018 according to BNM. Check this [page](https://stripe.com/docs/payments/fpx) for more details.
type PaymentMethodConfigurationCreateFPXParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateFPXDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateGiropayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// giropay is a German payment method based on online banking, introduced in 2006. It allows customers to complete transactions online using their online banking environment, with funds debited from their bank account. Depending on their bank, customers confirm payments on giropay using a second factor of authentication or a PIN. giropay accounts for 10% of online checkouts in Germany. Check this [page](https://stripe.com/docs/payments/giropay) for more details.
type PaymentMethodConfigurationCreateGiropayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateGiropayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateGooglePayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Google Pay allows customers to make payments in your app or website using any credit or debit card saved to their Google Account, including those from Google Play, YouTube, Chrome, or an Android device. Use the Google Pay API to request any credit or debit card stored in your customer's Google account. Check this [page](https://stripe.com/docs/google-pay) for more details.
type PaymentMethodConfigurationCreateGooglePayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateGooglePayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateGrabpayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// GrabPay is a payment method developed by [Grab](https://www.grab.com/sg/consumer/finance/pay/). GrabPay is a digital wallet - customers maintain a balance in their wallets that they pay out with. Check this [page](https://stripe.com/docs/payments/grabpay) for more details.
type PaymentMethodConfigurationCreateGrabpayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateGrabpayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateIDEALDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// iDEAL is a Netherlands-based payment method that allows customers to complete transactions online using their bank credentials. All major Dutch banks are members of Currence, the scheme that operates iDEAL, making it the most popular online payment method in the Netherlands with a share of online transactions close to 55%. Check this [page](https://stripe.com/docs/payments/ideal) for more details.
type PaymentMethodConfigurationCreateIDEALParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateIDEALDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateJCBDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// JCB is a credit card company based in Japan. JCB is currently available in Japan to businesses approved by JCB, and available to all businesses in Australia, Canada, Hong Kong, Japan, New Zealand, Singapore, Switzerland, United Kingdom, United States, and all countries in the European Economic Area except Iceland. Check this [page](https://support.stripe.com/questions/accepting-japan-credit-bureau-%28jcb%29-payments) for more details.
type PaymentMethodConfigurationCreateJCBParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateJCBDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateKakaoPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Kakao Pay is a popular local wallet available in South Korea.
type PaymentMethodConfigurationCreateKakaoPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateKakaoPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateKlarnaDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Klarna gives customers a range of [payment options](https://stripe.com/docs/payments/klarna#payment-options) during checkout. Available payment options vary depending on the customer's billing address and the transaction amount. These payment options make it convenient for customers to purchase items in all price ranges. Check this [page](https://stripe.com/docs/payments/klarna) for more details.
type PaymentMethodConfigurationCreateKlarnaParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateKlarnaDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateKonbiniDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Konbini allows customers in Japan to pay for bills and online purchases at convenience stores with cash. Check this [page](https://stripe.com/docs/payments/konbini) for more details.
type PaymentMethodConfigurationCreateKonbiniParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateKonbiniDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateKrCardDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Korean cards let users pay using locally issued cards from South Korea.
type PaymentMethodConfigurationCreateKrCardParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateKrCardDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateLinkDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// [Link](https://stripe.com/docs/payments/link) is a payment method network. With Link, users save their payment details once, then reuse that information to pay with one click for any business on the network.
type PaymentMethodConfigurationCreateLinkParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateLinkDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateMobilepayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// MobilePay is a [single-use](https://stripe.com/docs/payments/payment-methods#usage) card wallet payment method used in Denmark and Finland. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the MobilePay app. Check this [page](https://stripe.com/docs/payments/mobilepay) for more details.
type PaymentMethodConfigurationCreateMobilepayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateMobilepayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateMultibancoDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in Europe and the United States can accept Multibanco payments from customers in Portugal using [Sources](https://stripe.com/docs/sources)—a single integration path for creating payments using any supported method.
type PaymentMethodConfigurationCreateMultibancoParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateMultibancoDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateNaverPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Naver Pay is a popular local wallet available in South Korea.
type PaymentMethodConfigurationCreateNaverPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateNaverPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateNzBankAccountDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in New Zealand can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with a New Zeland bank account. Check this [page](https://stripe.com/docs/payments/nz-bank-account) for more details.
type PaymentMethodConfigurationCreateNzBankAccountParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateNzBankAccountDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateOXXODisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// OXXO is a Mexican chain of convenience stores with thousands of locations across Latin America and represents nearly 20% of online transactions in Mexico. OXXO allows customers to pay bills and online purchases in-store with cash. Check this [page](https://stripe.com/docs/payments/oxxo) for more details.
type PaymentMethodConfigurationCreateOXXOParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateOXXODisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateP24DisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Przelewy24 is a Poland-based payment method aggregator that allows customers to complete transactions online using bank transfers and other methods. Bank transfers account for 30% of online payments in Poland and Przelewy24 provides a way for customers to pay with over 165 banks. Check this [page](https://stripe.com/docs/payments/p24) for more details.
type PaymentMethodConfigurationCreateP24Params struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateP24DisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreatePayByBankDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Pay by bank is a redirect payment method backed by bank transfers. A customer is redirected to their bank to authorize a bank transfer for a given amount. This removes a lot of the error risks inherent in waiting for the customer to initiate a transfer themselves, and is less expensive than card payments.
type PaymentMethodConfigurationCreatePayByBankParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreatePayByBankDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreatePaycoDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// PAYCO is a [single-use](https://docs.stripe.com/payments/payment-methods#usage local wallet available in South Korea.
type PaymentMethodConfigurationCreatePaycoParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreatePaycoDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreatePayNowDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// PayNow is a Singapore-based payment method that allows customers to make a payment using their preferred app from participating banks and participating non-bank financial institutions. Check this [page](https://stripe.com/docs/payments/paynow) for more details.
type PaymentMethodConfigurationCreatePayNowParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreatePayNowDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreatePaypalDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// PayPal, a digital wallet popular with customers in Europe, allows your customers worldwide to pay using their PayPal account. Check this [page](https://stripe.com/docs/payments/paypal) for more details.
type PaymentMethodConfigurationCreatePaypalParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreatePaypalDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreatePixDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Pix is a payment method popular in Brazil. When paying with Pix, customers authenticate and approve payments by scanning a QR code in their preferred banking app. Check this [page](https://docs.stripe.com/payments/pix) for more details.
type PaymentMethodConfigurationCreatePixParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreatePixDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreatePromptPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// PromptPay is a Thailand-based payment method that allows customers to make a payment using their preferred app from participating banks. Check this [page](https://stripe.com/docs/payments/promptpay) for more details.
type PaymentMethodConfigurationCreatePromptPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreatePromptPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateRevolutPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Revolut Pay, developed by Revolut, a global finance app, is a digital wallet payment method. Revolut Pay uses the customer's stored balance or cards to fund the payment, and offers the option for non-Revolut customers to save their details after their first purchase.
type PaymentMethodConfigurationCreateRevolutPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateRevolutPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateSamsungPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Samsung Pay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage local wallet available in South Korea.
type PaymentMethodConfigurationCreateSamsungPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateSamsungPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateSatispayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Satispay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method where customers are required to [authenticate](https://docs.stripe.com/payments/payment-methods#customer-actions) their payment. Customers pay by being redirected from your website or app, authorizing the payment with Satispay, then returning to your website or app. You get [immediate notification](https://docs.stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
type PaymentMethodConfigurationCreateSatispayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateSatispayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateSEPADebitDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// The [Single Euro Payments Area (SEPA)](https://en.wikipedia.org/wiki/Single_Euro_Payments_Area) is an initiative of the European Union to simplify payments within and across member countries. SEPA established and enforced banking standards to allow for the direct debiting of every EUR-denominated bank account within the SEPA region, check this [page](https://stripe.com/docs/payments/sepa-debit) for more details.
type PaymentMethodConfigurationCreateSEPADebitParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateSEPADebitDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateSofortDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in Europe and the United States can use the [Payment Intents API](https://stripe.com/docs/payments/payment-intents)—a single integration path for creating payments using any supported method—to accept [Sofort](https://www.sofort.com/) payments from customers. Check this [page](https://stripe.com/docs/payments/sofort) for more details.
type PaymentMethodConfigurationCreateSofortParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateSofortDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateSwishDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Swish is a [real-time](https://stripe.com/docs/payments/real-time) payment method popular in Sweden. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the Swish mobile app and the Swedish BankID mobile app. Check this [page](https://stripe.com/docs/payments/swish) for more details.
type PaymentMethodConfigurationCreateSwishParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateSwishDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateTWINTDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Twint is a payment method popular in Switzerland. It allows customers to pay using their mobile phone. Check this [page](https://docs.stripe.com/payments/twint) for more details.
type PaymentMethodConfigurationCreateTWINTParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateTWINTDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateUSBankAccountDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in the United States can accept ACH direct debit payments from customers with a US bank account using the Automated Clearing House (ACH) payments system operated by Nacha. Check this [page](https://stripe.com/docs/payments/ach-direct-debit) for more details.
type PaymentMethodConfigurationCreateUSBankAccountParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateUSBankAccountDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateWeChatPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// WeChat, owned by Tencent, is China's leading mobile app with over 1 billion monthly active users. Chinese consumers can use WeChat Pay to pay for goods and services inside of businesses' apps and websites. WeChat Pay users buy most frequently in gaming, e-commerce, travel, online education, and food/nutrition. Check this [page](https://stripe.com/docs/payments/wechat-pay) for more details.
type PaymentMethodConfigurationCreateWeChatPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateWeChatPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationCreateZipDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Zip gives your customers a way to split purchases over a series of payments. Check this [page](https://stripe.com/docs/payments/zip) for more details like country availability.
type PaymentMethodConfigurationCreateZipParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationCreateZipDisplayPreferenceParams `form:"display_preference"`
}

// Creates a payment method configuration
type PaymentMethodConfigurationCreateParams struct {
	Params `form:"*"`
	// Canadian pre-authorized debit payments, check this [page](https://stripe.com/docs/payments/acss-debit) for more details like country availability.
	ACSSDebit *PaymentMethodConfigurationCreateACSSDebitParams `form:"acss_debit"`
	// [Affirm](https://www.affirm.com/) gives your customers a way to split purchases over a series of payments. Depending on the purchase, they can pay with four interest-free payments (Split Pay) or pay over a longer term (Installments), which might include interest. Check this [page](https://stripe.com/docs/payments/affirm) for more details like country availability.
	Affirm *PaymentMethodConfigurationCreateAffirmParams `form:"affirm"`
	// Afterpay gives your customers a way to pay for purchases in installments, check this [page](https://stripe.com/docs/payments/afterpay-clearpay) for more details like country availability. Afterpay is particularly popular among businesses selling fashion, beauty, and sports products.
	AfterpayClearpay *PaymentMethodConfigurationCreateAfterpayClearpayParams `form:"afterpay_clearpay"`
	// Alipay is a digital wallet in China that has more than a billion active users worldwide. Alipay users can pay on the web or on a mobile device using login credentials or their Alipay app. Alipay has a low dispute rate and reduces fraud by authenticating payments using the customer's login credentials. Check this [page](https://stripe.com/docs/payments/alipay) for more details.
	Alipay *PaymentMethodConfigurationCreateAlipayParams `form:"alipay"`
	// Alma is a Buy Now, Pay Later payment method that offers customers the ability to pay in 2, 3, or 4 installments.
	Alma *PaymentMethodConfigurationCreateAlmaParams `form:"alma"`
	// Amazon Pay is a wallet payment method that lets your customers check out the same way as on Amazon.
	AmazonPay *PaymentMethodConfigurationCreateAmazonPayParams `form:"amazon_pay"`
	// Stripe users can accept [Apple Pay](https://stripe.com/payments/apple-pay) in iOS applications in iOS 9 and later, and on the web in Safari starting with iOS 10 or macOS Sierra. There are no additional fees to process Apple Pay payments, and the [pricing](https://stripe.com/pricing) is the same as other card transactions. Check this [page](https://stripe.com/docs/apple-pay) for more details.
	ApplePay *PaymentMethodConfigurationCreateApplePayParams `form:"apple_pay"`
	// Apple Pay Later, a payment method for customers to buy now and pay later, gives your customers a way to split purchases into four installments across six weeks.
	ApplePayLater *PaymentMethodConfigurationCreateApplePayLaterParams `form:"apple_pay_later"`
	// Stripe users in Australia can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with an Australian bank account. Check this [page](https://stripe.com/docs/payments/au-becs-debit) for more details.
	AUBECSDebit *PaymentMethodConfigurationCreateAUBECSDebitParams `form:"au_becs_debit"`
	// Stripe users in the UK can accept Bacs Direct Debit payments from customers with a UK bank account, check this [page](https://stripe.com/docs/payments/payment-methods/bacs-debit) for more details.
	BACSDebit *PaymentMethodConfigurationCreateBACSDebitParams `form:"bacs_debit"`
	// Bancontact is the most popular online payment method in Belgium, with over 15 million cards in circulation. [Customers](https://stripe.com/docs/api/customers) use a Bancontact card or mobile app linked to a Belgian bank account to make online payments that are secure, guaranteed, and confirmed immediately. Check this [page](https://stripe.com/docs/payments/bancontact) for more details.
	Bancontact *PaymentMethodConfigurationCreateBancontactParams `form:"bancontact"`
	// Billie is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method that offers businesses Pay by Invoice where they offer payment terms ranging from 7-120 days. Customers are redirected from your website or app, authorize the payment with Billie, then return to your website or app. You get [immediate notification](https://docs.stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
	Billie *PaymentMethodConfigurationCreateBillieParams `form:"billie"`
	// BLIK is a [single use](https://stripe.com/docs/payments/payment-methods#usage) payment method that requires customers to authenticate their payments. When customers want to pay online using BLIK, they request a six-digit code from their banking application and enter it into the payment collection form. Check this [page](https://stripe.com/docs/payments/blik) for more details.
	BLIK *PaymentMethodConfigurationCreateBLIKParams `form:"blik"`
	// Boleto is an official (regulated by the Central Bank of Brazil) payment method in Brazil. Check this [page](https://stripe.com/docs/payments/boleto) for more details.
	Boleto *PaymentMethodConfigurationCreateBoletoParams `form:"boleto"`
	// Cards are a popular way for consumers and businesses to pay online or in person. Stripe supports global and local card networks.
	Card *PaymentMethodConfigurationCreateCardParams `form:"card"`
	// Cartes Bancaires is France's local card network. More than 95% of these cards are co-branded with either Visa or Mastercard, meaning you can process these cards over either Cartes Bancaires or the Visa or Mastercard networks. Check this [page](https://stripe.com/docs/payments/cartes-bancaires) for more details.
	CartesBancaires *PaymentMethodConfigurationCreateCartesBancairesParams `form:"cartes_bancaires"`
	// Cash App is a popular consumer app in the US that allows customers to bank, invest, send, and receive money using their digital wallet. Check this [page](https://stripe.com/docs/payments/cash-app-pay) for more details.
	CashApp *PaymentMethodConfigurationCreateCashAppParams `form:"cashapp"`
	// Uses a customer's [cash balance](https://stripe.com/docs/payments/customer-balance) for the payment. The cash balance can be funded via a bank transfer. Check this [page](https://stripe.com/docs/payments/bank-transfers) for more details.
	CustomerBalance *PaymentMethodConfigurationCreateCustomerBalanceParams `form:"customer_balance"`
	// EPS is an Austria-based payment method that allows customers to complete transactions online using their bank credentials. EPS is supported by all Austrian banks and is accepted by over 80% of Austrian online retailers. Check this [page](https://stripe.com/docs/payments/eps) for more details.
	EPS *PaymentMethodConfigurationCreateEPSParams `form:"eps"`
	// Specifies which fields in the response should be expanded.
	Expand []*string `form:"expand"`
	// Financial Process Exchange (FPX) is a Malaysia-based payment method that allows customers to complete transactions online using their bank credentials. Bank Negara Malaysia (BNM), the Central Bank of Malaysia, and eleven other major Malaysian financial institutions are members of the PayNet Group, which owns and operates FPX. It is one of the most popular online payment methods in Malaysia, with nearly 90 million transactions in 2018 according to BNM. Check this [page](https://stripe.com/docs/payments/fpx) for more details.
	FPX *PaymentMethodConfigurationCreateFPXParams `form:"fpx"`
	// giropay is a German payment method based on online banking, introduced in 2006. It allows customers to complete transactions online using their online banking environment, with funds debited from their bank account. Depending on their bank, customers confirm payments on giropay using a second factor of authentication or a PIN. giropay accounts for 10% of online checkouts in Germany. Check this [page](https://stripe.com/docs/payments/giropay) for more details.
	Giropay *PaymentMethodConfigurationCreateGiropayParams `form:"giropay"`
	// Google Pay allows customers to make payments in your app or website using any credit or debit card saved to their Google Account, including those from Google Play, YouTube, Chrome, or an Android device. Use the Google Pay API to request any credit or debit card stored in your customer's Google account. Check this [page](https://stripe.com/docs/google-pay) for more details.
	GooglePay *PaymentMethodConfigurationCreateGooglePayParams `form:"google_pay"`
	// GrabPay is a payment method developed by [Grab](https://www.grab.com/sg/consumer/finance/pay/). GrabPay is a digital wallet - customers maintain a balance in their wallets that they pay out with. Check this [page](https://stripe.com/docs/payments/grabpay) for more details.
	Grabpay *PaymentMethodConfigurationCreateGrabpayParams `form:"grabpay"`
	// iDEAL is a Netherlands-based payment method that allows customers to complete transactions online using their bank credentials. All major Dutch banks are members of Currence, the scheme that operates iDEAL, making it the most popular online payment method in the Netherlands with a share of online transactions close to 55%. Check this [page](https://stripe.com/docs/payments/ideal) for more details.
	IDEAL *PaymentMethodConfigurationCreateIDEALParams `form:"ideal"`
	// JCB is a credit card company based in Japan. JCB is currently available in Japan to businesses approved by JCB, and available to all businesses in Australia, Canada, Hong Kong, Japan, New Zealand, Singapore, Switzerland, United Kingdom, United States, and all countries in the European Economic Area except Iceland. Check this [page](https://support.stripe.com/questions/accepting-japan-credit-bureau-%28jcb%29-payments) for more details.
	JCB *PaymentMethodConfigurationCreateJCBParams `form:"jcb"`
	// Kakao Pay is a popular local wallet available in South Korea.
	KakaoPay *PaymentMethodConfigurationCreateKakaoPayParams `form:"kakao_pay"`
	// Klarna gives customers a range of [payment options](https://stripe.com/docs/payments/klarna#payment-options) during checkout. Available payment options vary depending on the customer's billing address and the transaction amount. These payment options make it convenient for customers to purchase items in all price ranges. Check this [page](https://stripe.com/docs/payments/klarna) for more details.
	Klarna *PaymentMethodConfigurationCreateKlarnaParams `form:"klarna"`
	// Konbini allows customers in Japan to pay for bills and online purchases at convenience stores with cash. Check this [page](https://stripe.com/docs/payments/konbini) for more details.
	Konbini *PaymentMethodConfigurationCreateKonbiniParams `form:"konbini"`
	// Korean cards let users pay using locally issued cards from South Korea.
	KrCard *PaymentMethodConfigurationCreateKrCardParams `form:"kr_card"`
	// [Link](https://stripe.com/docs/payments/link) is a payment method network. With Link, users save their payment details once, then reuse that information to pay with one click for any business on the network.
	Link *PaymentMethodConfigurationCreateLinkParams `form:"link"`
	// MobilePay is a [single-use](https://stripe.com/docs/payments/payment-methods#usage) card wallet payment method used in Denmark and Finland. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the MobilePay app. Check this [page](https://stripe.com/docs/payments/mobilepay) for more details.
	Mobilepay *PaymentMethodConfigurationCreateMobilepayParams `form:"mobilepay"`
	// Stripe users in Europe and the United States can accept Multibanco payments from customers in Portugal using [Sources](https://stripe.com/docs/sources)—a single integration path for creating payments using any supported method.
	Multibanco *PaymentMethodConfigurationCreateMultibancoParams `form:"multibanco"`
	// Configuration name.
	Name *string `form:"name"`
	// Naver Pay is a popular local wallet available in South Korea.
	NaverPay *PaymentMethodConfigurationCreateNaverPayParams `form:"naver_pay"`
	// Stripe users in New Zealand can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with a New Zeland bank account. Check this [page](https://stripe.com/docs/payments/nz-bank-account) for more details.
	NzBankAccount *PaymentMethodConfigurationCreateNzBankAccountParams `form:"nz_bank_account"`
	// OXXO is a Mexican chain of convenience stores with thousands of locations across Latin America and represents nearly 20% of online transactions in Mexico. OXXO allows customers to pay bills and online purchases in-store with cash. Check this [page](https://stripe.com/docs/payments/oxxo) for more details.
	OXXO *PaymentMethodConfigurationCreateOXXOParams `form:"oxxo"`
	// Przelewy24 is a Poland-based payment method aggregator that allows customers to complete transactions online using bank transfers and other methods. Bank transfers account for 30% of online payments in Poland and Przelewy24 provides a way for customers to pay with over 165 banks. Check this [page](https://stripe.com/docs/payments/p24) for more details.
	P24 *PaymentMethodConfigurationCreateP24Params `form:"p24"`
	// Configuration's parent configuration. Specify to create a child configuration.
	Parent *string `form:"parent"`
	// Pay by bank is a redirect payment method backed by bank transfers. A customer is redirected to their bank to authorize a bank transfer for a given amount. This removes a lot of the error risks inherent in waiting for the customer to initiate a transfer themselves, and is less expensive than card payments.
	PayByBank *PaymentMethodConfigurationCreatePayByBankParams `form:"pay_by_bank"`
	// PAYCO is a [single-use](https://docs.stripe.com/payments/payment-methods#usage local wallet available in South Korea.
	Payco *PaymentMethodConfigurationCreatePaycoParams `form:"payco"`
	// PayNow is a Singapore-based payment method that allows customers to make a payment using their preferred app from participating banks and participating non-bank financial institutions. Check this [page](https://stripe.com/docs/payments/paynow) for more details.
	PayNow *PaymentMethodConfigurationCreatePayNowParams `form:"paynow"`
	// PayPal, a digital wallet popular with customers in Europe, allows your customers worldwide to pay using their PayPal account. Check this [page](https://stripe.com/docs/payments/paypal) for more details.
	Paypal *PaymentMethodConfigurationCreatePaypalParams `form:"paypal"`
	// Pix is a payment method popular in Brazil. When paying with Pix, customers authenticate and approve payments by scanning a QR code in their preferred banking app. Check this [page](https://docs.stripe.com/payments/pix) for more details.
	Pix *PaymentMethodConfigurationCreatePixParams `form:"pix"`
	// PromptPay is a Thailand-based payment method that allows customers to make a payment using their preferred app from participating banks. Check this [page](https://stripe.com/docs/payments/promptpay) for more details.
	PromptPay *PaymentMethodConfigurationCreatePromptPayParams `form:"promptpay"`
	// Revolut Pay, developed by Revolut, a global finance app, is a digital wallet payment method. Revolut Pay uses the customer's stored balance or cards to fund the payment, and offers the option for non-Revolut customers to save their details after their first purchase.
	RevolutPay *PaymentMethodConfigurationCreateRevolutPayParams `form:"revolut_pay"`
	// Samsung Pay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage local wallet available in South Korea.
	SamsungPay *PaymentMethodConfigurationCreateSamsungPayParams `form:"samsung_pay"`
	// Satispay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method where customers are required to [authenticate](https://docs.stripe.com/payments/payment-methods#customer-actions) their payment. Customers pay by being redirected from your website or app, authorizing the payment with Satispay, then returning to your website or app. You get [immediate notification](https://docs.stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
	Satispay *PaymentMethodConfigurationCreateSatispayParams `form:"satispay"`
	// The [Single Euro Payments Area (SEPA)](https://en.wikipedia.org/wiki/Single_Euro_Payments_Area) is an initiative of the European Union to simplify payments within and across member countries. SEPA established and enforced banking standards to allow for the direct debiting of every EUR-denominated bank account within the SEPA region, check this [page](https://stripe.com/docs/payments/sepa-debit) for more details.
	SEPADebit *PaymentMethodConfigurationCreateSEPADebitParams `form:"sepa_debit"`
	// Stripe users in Europe and the United States can use the [Payment Intents API](https://stripe.com/docs/payments/payment-intents)—a single integration path for creating payments using any supported method—to accept [Sofort](https://www.sofort.com/) payments from customers. Check this [page](https://stripe.com/docs/payments/sofort) for more details.
	Sofort *PaymentMethodConfigurationCreateSofortParams `form:"sofort"`
	// Swish is a [real-time](https://stripe.com/docs/payments/real-time) payment method popular in Sweden. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the Swish mobile app and the Swedish BankID mobile app. Check this [page](https://stripe.com/docs/payments/swish) for more details.
	Swish *PaymentMethodConfigurationCreateSwishParams `form:"swish"`
	// Twint is a payment method popular in Switzerland. It allows customers to pay using their mobile phone. Check this [page](https://docs.stripe.com/payments/twint) for more details.
	TWINT *PaymentMethodConfigurationCreateTWINTParams `form:"twint"`
	// Stripe users in the United States can accept ACH direct debit payments from customers with a US bank account using the Automated Clearing House (ACH) payments system operated by Nacha. Check this [page](https://stripe.com/docs/payments/ach-direct-debit) for more details.
	USBankAccount *PaymentMethodConfigurationCreateUSBankAccountParams `form:"us_bank_account"`
	// WeChat, owned by Tencent, is China's leading mobile app with over 1 billion monthly active users. Chinese consumers can use WeChat Pay to pay for goods and services inside of businesses' apps and websites. WeChat Pay users buy most frequently in gaming, e-commerce, travel, online education, and food/nutrition. Check this [page](https://stripe.com/docs/payments/wechat-pay) for more details.
	WeChatPay *PaymentMethodConfigurationCreateWeChatPayParams `form:"wechat_pay"`
	// Zip gives your customers a way to split purchases over a series of payments. Check this [page](https://stripe.com/docs/payments/zip) for more details like country availability.
	Zip *PaymentMethodConfigurationCreateZipParams `form:"zip"`
}

// AddExpand appends a new field to expand.
func (p *PaymentMethodConfigurationCreateParams) AddExpand(f string) {
	p.Expand = append(p.Expand, &f)
}

// Retrieve payment method configuration
type PaymentMethodConfigurationRetrieveParams struct {
	Params `form:"*"`
	// Specifies which fields in the response should be expanded.
	Expand []*string `form:"expand"`
}

// AddExpand appends a new field to expand.
func (p *PaymentMethodConfigurationRetrieveParams) AddExpand(f string) {
	p.Expand = append(p.Expand, &f)
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateACSSDebitDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Canadian pre-authorized debit payments, check this [page](https://stripe.com/docs/payments/acss-debit) for more details like country availability.
type PaymentMethodConfigurationUpdateACSSDebitParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateACSSDebitDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateAffirmDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// [Affirm](https://www.affirm.com/) gives your customers a way to split purchases over a series of payments. Depending on the purchase, they can pay with four interest-free payments (Split Pay) or pay over a longer term (Installments), which might include interest. Check this [page](https://stripe.com/docs/payments/affirm) for more details like country availability.
type PaymentMethodConfigurationUpdateAffirmParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateAffirmDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateAfterpayClearpayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Afterpay gives your customers a way to pay for purchases in installments, check this [page](https://stripe.com/docs/payments/afterpay-clearpay) for more details like country availability. Afterpay is particularly popular among businesses selling fashion, beauty, and sports products.
type PaymentMethodConfigurationUpdateAfterpayClearpayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateAfterpayClearpayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateAlipayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Alipay is a digital wallet in China that has more than a billion active users worldwide. Alipay users can pay on the web or on a mobile device using login credentials or their Alipay app. Alipay has a low dispute rate and reduces fraud by authenticating payments using the customer's login credentials. Check this [page](https://stripe.com/docs/payments/alipay) for more details.
type PaymentMethodConfigurationUpdateAlipayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateAlipayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateAlmaDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Alma is a Buy Now, Pay Later payment method that offers customers the ability to pay in 2, 3, or 4 installments.
type PaymentMethodConfigurationUpdateAlmaParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateAlmaDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateAmazonPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Amazon Pay is a wallet payment method that lets your customers check out the same way as on Amazon.
type PaymentMethodConfigurationUpdateAmazonPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateAmazonPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateApplePayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users can accept [Apple Pay](https://stripe.com/payments/apple-pay) in iOS applications in iOS 9 and later, and on the web in Safari starting with iOS 10 or macOS Sierra. There are no additional fees to process Apple Pay payments, and the [pricing](https://stripe.com/pricing) is the same as other card transactions. Check this [page](https://stripe.com/docs/apple-pay) for more details.
type PaymentMethodConfigurationUpdateApplePayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateApplePayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateApplePayLaterDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Apple Pay Later, a payment method for customers to buy now and pay later, gives your customers a way to split purchases into four installments across six weeks.
type PaymentMethodConfigurationUpdateApplePayLaterParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateApplePayLaterDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateAUBECSDebitDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in Australia can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with an Australian bank account. Check this [page](https://stripe.com/docs/payments/au-becs-debit) for more details.
type PaymentMethodConfigurationUpdateAUBECSDebitParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateAUBECSDebitDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateBACSDebitDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in the UK can accept Bacs Direct Debit payments from customers with a UK bank account, check this [page](https://stripe.com/docs/payments/payment-methods/bacs-debit) for more details.
type PaymentMethodConfigurationUpdateBACSDebitParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateBACSDebitDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateBancontactDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Bancontact is the most popular online payment method in Belgium, with over 15 million cards in circulation. [Customers](https://stripe.com/docs/api/customers) use a Bancontact card or mobile app linked to a Belgian bank account to make online payments that are secure, guaranteed, and confirmed immediately. Check this [page](https://stripe.com/docs/payments/bancontact) for more details.
type PaymentMethodConfigurationUpdateBancontactParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateBancontactDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateBillieDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Billie is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method that offers businesses Pay by Invoice where they offer payment terms ranging from 7-120 days. Customers are redirected from your website or app, authorize the payment with Billie, then return to your website or app. You get [immediate notification](https://docs.stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
type PaymentMethodConfigurationUpdateBillieParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateBillieDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateBLIKDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// BLIK is a [single use](https://stripe.com/docs/payments/payment-methods#usage) payment method that requires customers to authenticate their payments. When customers want to pay online using BLIK, they request a six-digit code from their banking application and enter it into the payment collection form. Check this [page](https://stripe.com/docs/payments/blik) for more details.
type PaymentMethodConfigurationUpdateBLIKParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateBLIKDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateBoletoDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Boleto is an official (regulated by the Central Bank of Brazil) payment method in Brazil. Check this [page](https://stripe.com/docs/payments/boleto) for more details.
type PaymentMethodConfigurationUpdateBoletoParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateBoletoDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateCardDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Cards are a popular way for consumers and businesses to pay online or in person. Stripe supports global and local card networks.
type PaymentMethodConfigurationUpdateCardParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateCardDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateCartesBancairesDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Cartes Bancaires is France's local card network. More than 95% of these cards are co-branded with either Visa or Mastercard, meaning you can process these cards over either Cartes Bancaires or the Visa or Mastercard networks. Check this [page](https://stripe.com/docs/payments/cartes-bancaires) for more details.
type PaymentMethodConfigurationUpdateCartesBancairesParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateCartesBancairesDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateCashAppDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Cash App is a popular consumer app in the US that allows customers to bank, invest, send, and receive money using their digital wallet. Check this [page](https://stripe.com/docs/payments/cash-app-pay) for more details.
type PaymentMethodConfigurationUpdateCashAppParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateCashAppDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateCustomerBalanceDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Uses a customer's [cash balance](https://stripe.com/docs/payments/customer-balance) for the payment. The cash balance can be funded via a bank transfer. Check this [page](https://stripe.com/docs/payments/bank-transfers) for more details.
type PaymentMethodConfigurationUpdateCustomerBalanceParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateCustomerBalanceDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateEPSDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// EPS is an Austria-based payment method that allows customers to complete transactions online using their bank credentials. EPS is supported by all Austrian banks and is accepted by over 80% of Austrian online retailers. Check this [page](https://stripe.com/docs/payments/eps) for more details.
type PaymentMethodConfigurationUpdateEPSParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateEPSDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateFPXDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Financial Process Exchange (FPX) is a Malaysia-based payment method that allows customers to complete transactions online using their bank credentials. Bank Negara Malaysia (BNM), the Central Bank of Malaysia, and eleven other major Malaysian financial institutions are members of the PayNet Group, which owns and operates FPX. It is one of the most popular online payment methods in Malaysia, with nearly 90 million transactions in 2018 according to BNM. Check this [page](https://stripe.com/docs/payments/fpx) for more details.
type PaymentMethodConfigurationUpdateFPXParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateFPXDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateGiropayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// giropay is a German payment method based on online banking, introduced in 2006. It allows customers to complete transactions online using their online banking environment, with funds debited from their bank account. Depending on their bank, customers confirm payments on giropay using a second factor of authentication or a PIN. giropay accounts for 10% of online checkouts in Germany. Check this [page](https://stripe.com/docs/payments/giropay) for more details.
type PaymentMethodConfigurationUpdateGiropayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateGiropayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateGooglePayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Google Pay allows customers to make payments in your app or website using any credit or debit card saved to their Google Account, including those from Google Play, YouTube, Chrome, or an Android device. Use the Google Pay API to request any credit or debit card stored in your customer's Google account. Check this [page](https://stripe.com/docs/google-pay) for more details.
type PaymentMethodConfigurationUpdateGooglePayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateGooglePayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateGrabpayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// GrabPay is a payment method developed by [Grab](https://www.grab.com/sg/consumer/finance/pay/). GrabPay is a digital wallet - customers maintain a balance in their wallets that they pay out with. Check this [page](https://stripe.com/docs/payments/grabpay) for more details.
type PaymentMethodConfigurationUpdateGrabpayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateGrabpayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateIDEALDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// iDEAL is a Netherlands-based payment method that allows customers to complete transactions online using their bank credentials. All major Dutch banks are members of Currence, the scheme that operates iDEAL, making it the most popular online payment method in the Netherlands with a share of online transactions close to 55%. Check this [page](https://stripe.com/docs/payments/ideal) for more details.
type PaymentMethodConfigurationUpdateIDEALParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateIDEALDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateJCBDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// JCB is a credit card company based in Japan. JCB is currently available in Japan to businesses approved by JCB, and available to all businesses in Australia, Canada, Hong Kong, Japan, New Zealand, Singapore, Switzerland, United Kingdom, United States, and all countries in the European Economic Area except Iceland. Check this [page](https://support.stripe.com/questions/accepting-japan-credit-bureau-%28jcb%29-payments) for more details.
type PaymentMethodConfigurationUpdateJCBParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateJCBDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateKakaoPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Kakao Pay is a popular local wallet available in South Korea.
type PaymentMethodConfigurationUpdateKakaoPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateKakaoPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateKlarnaDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Klarna gives customers a range of [payment options](https://stripe.com/docs/payments/klarna#payment-options) during checkout. Available payment options vary depending on the customer's billing address and the transaction amount. These payment options make it convenient for customers to purchase items in all price ranges. Check this [page](https://stripe.com/docs/payments/klarna) for more details.
type PaymentMethodConfigurationUpdateKlarnaParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateKlarnaDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateKonbiniDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Konbini allows customers in Japan to pay for bills and online purchases at convenience stores with cash. Check this [page](https://stripe.com/docs/payments/konbini) for more details.
type PaymentMethodConfigurationUpdateKonbiniParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateKonbiniDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateKrCardDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Korean cards let users pay using locally issued cards from South Korea.
type PaymentMethodConfigurationUpdateKrCardParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateKrCardDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateLinkDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// [Link](https://stripe.com/docs/payments/link) is a payment method network. With Link, users save their payment details once, then reuse that information to pay with one click for any business on the network.
type PaymentMethodConfigurationUpdateLinkParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateLinkDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateMobilepayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// MobilePay is a [single-use](https://stripe.com/docs/payments/payment-methods#usage) card wallet payment method used in Denmark and Finland. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the MobilePay app. Check this [page](https://stripe.com/docs/payments/mobilepay) for more details.
type PaymentMethodConfigurationUpdateMobilepayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateMobilepayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateMultibancoDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in Europe and the United States can accept Multibanco payments from customers in Portugal using [Sources](https://stripe.com/docs/sources)—a single integration path for creating payments using any supported method.
type PaymentMethodConfigurationUpdateMultibancoParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateMultibancoDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateNaverPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Naver Pay is a popular local wallet available in South Korea.
type PaymentMethodConfigurationUpdateNaverPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateNaverPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateNzBankAccountDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in New Zealand can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with a New Zeland bank account. Check this [page](https://stripe.com/docs/payments/nz-bank-account) for more details.
type PaymentMethodConfigurationUpdateNzBankAccountParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateNzBankAccountDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateOXXODisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// OXXO is a Mexican chain of convenience stores with thousands of locations across Latin America and represents nearly 20% of online transactions in Mexico. OXXO allows customers to pay bills and online purchases in-store with cash. Check this [page](https://stripe.com/docs/payments/oxxo) for more details.
type PaymentMethodConfigurationUpdateOXXOParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateOXXODisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateP24DisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Przelewy24 is a Poland-based payment method aggregator that allows customers to complete transactions online using bank transfers and other methods. Bank transfers account for 30% of online payments in Poland and Przelewy24 provides a way for customers to pay with over 165 banks. Check this [page](https://stripe.com/docs/payments/p24) for more details.
type PaymentMethodConfigurationUpdateP24Params struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateP24DisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdatePayByBankDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Pay by bank is a redirect payment method backed by bank transfers. A customer is redirected to their bank to authorize a bank transfer for a given amount. This removes a lot of the error risks inherent in waiting for the customer to initiate a transfer themselves, and is less expensive than card payments.
type PaymentMethodConfigurationUpdatePayByBankParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdatePayByBankDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdatePaycoDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// PAYCO is a [single-use](https://docs.stripe.com/payments/payment-methods#usage local wallet available in South Korea.
type PaymentMethodConfigurationUpdatePaycoParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdatePaycoDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdatePayNowDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// PayNow is a Singapore-based payment method that allows customers to make a payment using their preferred app from participating banks and participating non-bank financial institutions. Check this [page](https://stripe.com/docs/payments/paynow) for more details.
type PaymentMethodConfigurationUpdatePayNowParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdatePayNowDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdatePaypalDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// PayPal, a digital wallet popular with customers in Europe, allows your customers worldwide to pay using their PayPal account. Check this [page](https://stripe.com/docs/payments/paypal) for more details.
type PaymentMethodConfigurationUpdatePaypalParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdatePaypalDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdatePixDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Pix is a payment method popular in Brazil. When paying with Pix, customers authenticate and approve payments by scanning a QR code in their preferred banking app. Check this [page](https://docs.stripe.com/payments/pix) for more details.
type PaymentMethodConfigurationUpdatePixParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdatePixDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdatePromptPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// PromptPay is a Thailand-based payment method that allows customers to make a payment using their preferred app from participating banks. Check this [page](https://stripe.com/docs/payments/promptpay) for more details.
type PaymentMethodConfigurationUpdatePromptPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdatePromptPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateRevolutPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Revolut Pay, developed by Revolut, a global finance app, is a digital wallet payment method. Revolut Pay uses the customer's stored balance or cards to fund the payment, and offers the option for non-Revolut customers to save their details after their first purchase.
type PaymentMethodConfigurationUpdateRevolutPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateRevolutPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateSamsungPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Samsung Pay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage local wallet available in South Korea.
type PaymentMethodConfigurationUpdateSamsungPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateSamsungPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateSatispayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Satispay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method where customers are required to [authenticate](https://docs.stripe.com/payments/payment-methods#customer-actions) their payment. Customers pay by being redirected from your website or app, authorizing the payment with Satispay, then returning to your website or app. You get [immediate notification](https://docs.stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
type PaymentMethodConfigurationUpdateSatispayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateSatispayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateSEPADebitDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// The [Single Euro Payments Area (SEPA)](https://en.wikipedia.org/wiki/Single_Euro_Payments_Area) is an initiative of the European Union to simplify payments within and across member countries. SEPA established and enforced banking standards to allow for the direct debiting of every EUR-denominated bank account within the SEPA region, check this [page](https://stripe.com/docs/payments/sepa-debit) for more details.
type PaymentMethodConfigurationUpdateSEPADebitParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateSEPADebitDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateSofortDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in Europe and the United States can use the [Payment Intents API](https://stripe.com/docs/payments/payment-intents)—a single integration path for creating payments using any supported method—to accept [Sofort](https://www.sofort.com/) payments from customers. Check this [page](https://stripe.com/docs/payments/sofort) for more details.
type PaymentMethodConfigurationUpdateSofortParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateSofortDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateSwishDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Swish is a [real-time](https://stripe.com/docs/payments/real-time) payment method popular in Sweden. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the Swish mobile app and the Swedish BankID mobile app. Check this [page](https://stripe.com/docs/payments/swish) for more details.
type PaymentMethodConfigurationUpdateSwishParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateSwishDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateTWINTDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Twint is a payment method popular in Switzerland. It allows customers to pay using their mobile phone. Check this [page](https://docs.stripe.com/payments/twint) for more details.
type PaymentMethodConfigurationUpdateTWINTParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateTWINTDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateUSBankAccountDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Stripe users in the United States can accept ACH direct debit payments from customers with a US bank account using the Automated Clearing House (ACH) payments system operated by Nacha. Check this [page](https://stripe.com/docs/payments/ach-direct-debit) for more details.
type PaymentMethodConfigurationUpdateUSBankAccountParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateUSBankAccountDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateWeChatPayDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// WeChat, owned by Tencent, is China's leading mobile app with over 1 billion monthly active users. Chinese consumers can use WeChat Pay to pay for goods and services inside of businesses' apps and websites. WeChat Pay users buy most frequently in gaming, e-commerce, travel, online education, and food/nutrition. Check this [page](https://stripe.com/docs/payments/wechat-pay) for more details.
type PaymentMethodConfigurationUpdateWeChatPayParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateWeChatPayDisplayPreferenceParams `form:"display_preference"`
}

// Whether or not the payment method should be displayed.
type PaymentMethodConfigurationUpdateZipDisplayPreferenceParams struct {
	// The account's preference for whether or not to display this payment method.
	Preference *string `form:"preference"`
}

// Zip gives your customers a way to split purchases over a series of payments. Check this [page](https://stripe.com/docs/payments/zip) for more details like country availability.
type PaymentMethodConfigurationUpdateZipParams struct {
	// Whether or not the payment method should be displayed.
	DisplayPreference *PaymentMethodConfigurationUpdateZipDisplayPreferenceParams `form:"display_preference"`
}

// Update payment method configuration
type PaymentMethodConfigurationUpdateParams struct {
	Params `form:"*"`
	// Canadian pre-authorized debit payments, check this [page](https://stripe.com/docs/payments/acss-debit) for more details like country availability.
	ACSSDebit *PaymentMethodConfigurationUpdateACSSDebitParams `form:"acss_debit"`
	// Whether the configuration can be used for new payments.
	Active *bool `form:"active"`
	// [Affirm](https://www.affirm.com/) gives your customers a way to split purchases over a series of payments. Depending on the purchase, they can pay with four interest-free payments (Split Pay) or pay over a longer term (Installments), which might include interest. Check this [page](https://stripe.com/docs/payments/affirm) for more details like country availability.
	Affirm *PaymentMethodConfigurationUpdateAffirmParams `form:"affirm"`
	// Afterpay gives your customers a way to pay for purchases in installments, check this [page](https://stripe.com/docs/payments/afterpay-clearpay) for more details like country availability. Afterpay is particularly popular among businesses selling fashion, beauty, and sports products.
	AfterpayClearpay *PaymentMethodConfigurationUpdateAfterpayClearpayParams `form:"afterpay_clearpay"`
	// Alipay is a digital wallet in China that has more than a billion active users worldwide. Alipay users can pay on the web or on a mobile device using login credentials or their Alipay app. Alipay has a low dispute rate and reduces fraud by authenticating payments using the customer's login credentials. Check this [page](https://stripe.com/docs/payments/alipay) for more details.
	Alipay *PaymentMethodConfigurationUpdateAlipayParams `form:"alipay"`
	// Alma is a Buy Now, Pay Later payment method that offers customers the ability to pay in 2, 3, or 4 installments.
	Alma *PaymentMethodConfigurationUpdateAlmaParams `form:"alma"`
	// Amazon Pay is a wallet payment method that lets your customers check out the same way as on Amazon.
	AmazonPay *PaymentMethodConfigurationUpdateAmazonPayParams `form:"amazon_pay"`
	// Stripe users can accept [Apple Pay](https://stripe.com/payments/apple-pay) in iOS applications in iOS 9 and later, and on the web in Safari starting with iOS 10 or macOS Sierra. There are no additional fees to process Apple Pay payments, and the [pricing](https://stripe.com/pricing) is the same as other card transactions. Check this [page](https://stripe.com/docs/apple-pay) for more details.
	ApplePay *PaymentMethodConfigurationUpdateApplePayParams `form:"apple_pay"`
	// Apple Pay Later, a payment method for customers to buy now and pay later, gives your customers a way to split purchases into four installments across six weeks.
	ApplePayLater *PaymentMethodConfigurationUpdateApplePayLaterParams `form:"apple_pay_later"`
	// Stripe users in Australia can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with an Australian bank account. Check this [page](https://stripe.com/docs/payments/au-becs-debit) for more details.
	AUBECSDebit *PaymentMethodConfigurationUpdateAUBECSDebitParams `form:"au_becs_debit"`
	// Stripe users in the UK can accept Bacs Direct Debit payments from customers with a UK bank account, check this [page](https://stripe.com/docs/payments/payment-methods/bacs-debit) for more details.
	BACSDebit *PaymentMethodConfigurationUpdateBACSDebitParams `form:"bacs_debit"`
	// Bancontact is the most popular online payment method in Belgium, with over 15 million cards in circulation. [Customers](https://stripe.com/docs/api/customers) use a Bancontact card or mobile app linked to a Belgian bank account to make online payments that are secure, guaranteed, and confirmed immediately. Check this [page](https://stripe.com/docs/payments/bancontact) for more details.
	Bancontact *PaymentMethodConfigurationUpdateBancontactParams `form:"bancontact"`
	// Billie is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method that offers businesses Pay by Invoice where they offer payment terms ranging from 7-120 days. Customers are redirected from your website or app, authorize the payment with Billie, then return to your website or app. You get [immediate notification](https://docs.stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
	Billie *PaymentMethodConfigurationUpdateBillieParams `form:"billie"`
	// BLIK is a [single use](https://stripe.com/docs/payments/payment-methods#usage) payment method that requires customers to authenticate their payments. When customers want to pay online using BLIK, they request a six-digit code from their banking application and enter it into the payment collection form. Check this [page](https://stripe.com/docs/payments/blik) for more details.
	BLIK *PaymentMethodConfigurationUpdateBLIKParams `form:"blik"`
	// Boleto is an official (regulated by the Central Bank of Brazil) payment method in Brazil. Check this [page](https://stripe.com/docs/payments/boleto) for more details.
	Boleto *PaymentMethodConfigurationUpdateBoletoParams `form:"boleto"`
	// Cards are a popular way for consumers and businesses to pay online or in person. Stripe supports global and local card networks.
	Card *PaymentMethodConfigurationUpdateCardParams `form:"card"`
	// Cartes Bancaires is France's local card network. More than 95% of these cards are co-branded with either Visa or Mastercard, meaning you can process these cards over either Cartes Bancaires or the Visa or Mastercard networks. Check this [page](https://stripe.com/docs/payments/cartes-bancaires) for more details.
	CartesBancaires *PaymentMethodConfigurationUpdateCartesBancairesParams `form:"cartes_bancaires"`
	// Cash App is a popular consumer app in the US that allows customers to bank, invest, send, and receive money using their digital wallet. Check this [page](https://stripe.com/docs/payments/cash-app-pay) for more details.
	CashApp *PaymentMethodConfigurationUpdateCashAppParams `form:"cashapp"`
	// Uses a customer's [cash balance](https://stripe.com/docs/payments/customer-balance) for the payment. The cash balance can be funded via a bank transfer. Check this [page](https://stripe.com/docs/payments/bank-transfers) for more details.
	CustomerBalance *PaymentMethodConfigurationUpdateCustomerBalanceParams `form:"customer_balance"`
	// EPS is an Austria-based payment method that allows customers to complete transactions online using their bank credentials. EPS is supported by all Austrian banks and is accepted by over 80% of Austrian online retailers. Check this [page](https://stripe.com/docs/payments/eps) for more details.
	EPS *PaymentMethodConfigurationUpdateEPSParams `form:"eps"`
	// Specifies which fields in the response should be expanded.
	Expand []*string `form:"expand"`
	// Financial Process Exchange (FPX) is a Malaysia-based payment method that allows customers to complete transactions online using their bank credentials. Bank Negara Malaysia (BNM), the Central Bank of Malaysia, and eleven other major Malaysian financial institutions are members of the PayNet Group, which owns and operates FPX. It is one of the most popular online payment methods in Malaysia, with nearly 90 million transactions in 2018 according to BNM. Check this [page](https://stripe.com/docs/payments/fpx) for more details.
	FPX *PaymentMethodConfigurationUpdateFPXParams `form:"fpx"`
	// giropay is a German payment method based on online banking, introduced in 2006. It allows customers to complete transactions online using their online banking environment, with funds debited from their bank account. Depending on their bank, customers confirm payments on giropay using a second factor of authentication or a PIN. giropay accounts for 10% of online checkouts in Germany. Check this [page](https://stripe.com/docs/payments/giropay) for more details.
	Giropay *PaymentMethodConfigurationUpdateGiropayParams `form:"giropay"`
	// Google Pay allows customers to make payments in your app or website using any credit or debit card saved to their Google Account, including those from Google Play, YouTube, Chrome, or an Android device. Use the Google Pay API to request any credit or debit card stored in your customer's Google account. Check this [page](https://stripe.com/docs/google-pay) for more details.
	GooglePay *PaymentMethodConfigurationUpdateGooglePayParams `form:"google_pay"`
	// GrabPay is a payment method developed by [Grab](https://www.grab.com/sg/consumer/finance/pay/). GrabPay is a digital wallet - customers maintain a balance in their wallets that they pay out with. Check this [page](https://stripe.com/docs/payments/grabpay) for more details.
	Grabpay *PaymentMethodConfigurationUpdateGrabpayParams `form:"grabpay"`
	// iDEAL is a Netherlands-based payment method that allows customers to complete transactions online using their bank credentials. All major Dutch banks are members of Currence, the scheme that operates iDEAL, making it the most popular online payment method in the Netherlands with a share of online transactions close to 55%. Check this [page](https://stripe.com/docs/payments/ideal) for more details.
	IDEAL *PaymentMethodConfigurationUpdateIDEALParams `form:"ideal"`
	// JCB is a credit card company based in Japan. JCB is currently available in Japan to businesses approved by JCB, and available to all businesses in Australia, Canada, Hong Kong, Japan, New Zealand, Singapore, Switzerland, United Kingdom, United States, and all countries in the European Economic Area except Iceland. Check this [page](https://support.stripe.com/questions/accepting-japan-credit-bureau-%28jcb%29-payments) for more details.
	JCB *PaymentMethodConfigurationUpdateJCBParams `form:"jcb"`
	// Kakao Pay is a popular local wallet available in South Korea.
	KakaoPay *PaymentMethodConfigurationUpdateKakaoPayParams `form:"kakao_pay"`
	// Klarna gives customers a range of [payment options](https://stripe.com/docs/payments/klarna#payment-options) during checkout. Available payment options vary depending on the customer's billing address and the transaction amount. These payment options make it convenient for customers to purchase items in all price ranges. Check this [page](https://stripe.com/docs/payments/klarna) for more details.
	Klarna *PaymentMethodConfigurationUpdateKlarnaParams `form:"klarna"`
	// Konbini allows customers in Japan to pay for bills and online purchases at convenience stores with cash. Check this [page](https://stripe.com/docs/payments/konbini) for more details.
	Konbini *PaymentMethodConfigurationUpdateKonbiniParams `form:"konbini"`
	// Korean cards let users pay using locally issued cards from South Korea.
	KrCard *PaymentMethodConfigurationUpdateKrCardParams `form:"kr_card"`
	// [Link](https://stripe.com/docs/payments/link) is a payment method network. With Link, users save their payment details once, then reuse that information to pay with one click for any business on the network.
	Link *PaymentMethodConfigurationUpdateLinkParams `form:"link"`
	// MobilePay is a [single-use](https://stripe.com/docs/payments/payment-methods#usage) card wallet payment method used in Denmark and Finland. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the MobilePay app. Check this [page](https://stripe.com/docs/payments/mobilepay) for more details.
	Mobilepay *PaymentMethodConfigurationUpdateMobilepayParams `form:"mobilepay"`
	// Stripe users in Europe and the United States can accept Multibanco payments from customers in Portugal using [Sources](https://stripe.com/docs/sources)—a single integration path for creating payments using any supported method.
	Multibanco *PaymentMethodConfigurationUpdateMultibancoParams `form:"multibanco"`
	// Configuration name.
	Name *string `form:"name"`
	// Naver Pay is a popular local wallet available in South Korea.
	NaverPay *PaymentMethodConfigurationUpdateNaverPayParams `form:"naver_pay"`
	// Stripe users in New Zealand can accept Bulk Electronic Clearing System (BECS) direct debit payments from customers with a New Zeland bank account. Check this [page](https://stripe.com/docs/payments/nz-bank-account) for more details.
	NzBankAccount *PaymentMethodConfigurationUpdateNzBankAccountParams `form:"nz_bank_account"`
	// OXXO is a Mexican chain of convenience stores with thousands of locations across Latin America and represents nearly 20% of online transactions in Mexico. OXXO allows customers to pay bills and online purchases in-store with cash. Check this [page](https://stripe.com/docs/payments/oxxo) for more details.
	OXXO *PaymentMethodConfigurationUpdateOXXOParams `form:"oxxo"`
	// Przelewy24 is a Poland-based payment method aggregator that allows customers to complete transactions online using bank transfers and other methods. Bank transfers account for 30% of online payments in Poland and Przelewy24 provides a way for customers to pay with over 165 banks. Check this [page](https://stripe.com/docs/payments/p24) for more details.
	P24 *PaymentMethodConfigurationUpdateP24Params `form:"p24"`
	// Pay by bank is a redirect payment method backed by bank transfers. A customer is redirected to their bank to authorize a bank transfer for a given amount. This removes a lot of the error risks inherent in waiting for the customer to initiate a transfer themselves, and is less expensive than card payments.
	PayByBank *PaymentMethodConfigurationUpdatePayByBankParams `form:"pay_by_bank"`
	// PAYCO is a [single-use](https://docs.stripe.com/payments/payment-methods#usage local wallet available in South Korea.
	Payco *PaymentMethodConfigurationUpdatePaycoParams `form:"payco"`
	// PayNow is a Singapore-based payment method that allows customers to make a payment using their preferred app from participating banks and participating non-bank financial institutions. Check this [page](https://stripe.com/docs/payments/paynow) for more details.
	PayNow *PaymentMethodConfigurationUpdatePayNowParams `form:"paynow"`
	// PayPal, a digital wallet popular with customers in Europe, allows your customers worldwide to pay using their PayPal account. Check this [page](https://stripe.com/docs/payments/paypal) for more details.
	Paypal *PaymentMethodConfigurationUpdatePaypalParams `form:"paypal"`
	// Pix is a payment method popular in Brazil. When paying with Pix, customers authenticate and approve payments by scanning a QR code in their preferred banking app. Check this [page](https://docs.stripe.com/payments/pix) for more details.
	Pix *PaymentMethodConfigurationUpdatePixParams `form:"pix"`
	// PromptPay is a Thailand-based payment method that allows customers to make a payment using their preferred app from participating banks. Check this [page](https://stripe.com/docs/payments/promptpay) for more details.
	PromptPay *PaymentMethodConfigurationUpdatePromptPayParams `form:"promptpay"`
	// Revolut Pay, developed by Revolut, a global finance app, is a digital wallet payment method. Revolut Pay uses the customer's stored balance or cards to fund the payment, and offers the option for non-Revolut customers to save their details after their first purchase.
	RevolutPay *PaymentMethodConfigurationUpdateRevolutPayParams `form:"revolut_pay"`
	// Samsung Pay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage local wallet available in South Korea.
	SamsungPay *PaymentMethodConfigurationUpdateSamsungPayParams `form:"samsung_pay"`
	// Satispay is a [single-use](https://docs.stripe.com/payments/payment-methods#usage) payment method where customers are required to [authenticate](https://docs.stripe.com/payments/payment-methods#customer-actions) their payment. Customers pay by being redirected from your website or app, authorizing the payment with Satispay, then returning to your website or app. You get [immediate notification](https://docs.stripe.com/payments/payment-methods#payment-notification) of whether the payment succeeded or failed.
	Satispay *PaymentMethodConfigurationUpdateSatispayParams `form:"satispay"`
	// The [Single Euro Payments Area (SEPA)](https://en.wikipedia.org/wiki/Single_Euro_Payments_Area) is an initiative of the European Union to simplify payments within and across member countries. SEPA established and enforced banking standards to allow for the direct debiting of every EUR-denominated bank account within the SEPA region, check this [page](https://stripe.com/docs/payments/sepa-debit) for more details.
	SEPADebit *PaymentMethodConfigurationUpdateSEPADebitParams `form:"sepa_debit"`
	// Stripe users in Europe and the United States can use the [Payment Intents API](https://stripe.com/docs/payments/payment-intents)—a single integration path for creating payments using any supported method—to accept [Sofort](https://www.sofort.com/) payments from customers. Check this [page](https://stripe.com/docs/payments/sofort) for more details.
	Sofort *PaymentMethodConfigurationUpdateSofortParams `form:"sofort"`
	// Swish is a [real-time](https://stripe.com/docs/payments/real-time) payment method popular in Sweden. It allows customers to [authenticate and approve](https://stripe.com/docs/payments/payment-methods#customer-actions) payments using the Swish mobile app and the Swedish BankID mobile app. Check this [page](https://stripe.com/docs/payments/swish) for more details.
	Swish *PaymentMethodConfigurationUpdateSwishParams `form:"swish"`
	// Twint is a payment method popular in Switzerland. It allows customers to pay using their mobile phone. Check this [page](https://docs.stripe.com/payments/twint) for more details.
	TWINT *PaymentMethodConfigurationUpdateTWINTParams `form:"twint"`
	// Stripe users in the United States can accept ACH direct debit payments from customers with a US bank account using the Automated Clearing House (ACH) payments system operated by Nacha. Check this [page](https://stripe.com/docs/payments/ach-direct-debit) for more details.
	USBankAccount *PaymentMethodConfigurationUpdateUSBankAccountParams `form:"us_bank_account"`
	// WeChat, owned by Tencent, is China's leading mobile app with over 1 billion monthly active users. Chinese consumers can use WeChat Pay to pay for goods and services inside of businesses' apps and websites. WeChat Pay users buy most frequently in gaming, e-commerce, travel, online education, and food/nutrition. Check this [page](https://stripe.com/docs/payments/wechat-pay) for more details.
	WeChatPay *PaymentMethodConfigurationUpdateWeChatPayParams `form:"wechat_pay"`
	// Zip gives your customers a way to split purchases over a series of payments. Check this [page](https://stripe.com/docs/payments/zip) for more details like country availability.
	Zip *PaymentMethodConfigurationUpdateZipParams `form:"zip"`
}

// AddExpand appends a new field to expand.
func (p *PaymentMethodConfigurationUpdateParams) AddExpand(f string) {
	p.Expand = append(p.Expand, &f)
}

type PaymentMethodConfigurationACSSDebitDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationACSSDebitDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationACSSDebitDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationACSSDebit struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                  `json:"available"`
	DisplayPreference *PaymentMethodConfigurationACSSDebitDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationAffirmDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationAffirmDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationAffirmDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationAffirm struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                               `json:"available"`
	DisplayPreference *PaymentMethodConfigurationAffirmDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationAfterpayClearpayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationAfterpayClearpayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationAfterpayClearpayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationAfterpayClearpay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                         `json:"available"`
	DisplayPreference *PaymentMethodConfigurationAfterpayClearpayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationAlipayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationAlipayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationAlipayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationAlipay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                               `json:"available"`
	DisplayPreference *PaymentMethodConfigurationAlipayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationAlmaDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationAlmaDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationAlmaDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationAlma struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                             `json:"available"`
	DisplayPreference *PaymentMethodConfigurationAlmaDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationAmazonPayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationAmazonPayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationAmazonPayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationAmazonPay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                  `json:"available"`
	DisplayPreference *PaymentMethodConfigurationAmazonPayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationApplePayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationApplePayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationApplePayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationApplePay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                 `json:"available"`
	DisplayPreference *PaymentMethodConfigurationApplePayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationAUBECSDebitDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationAUBECSDebitDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationAUBECSDebitDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationAUBECSDebit struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                    `json:"available"`
	DisplayPreference *PaymentMethodConfigurationAUBECSDebitDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationBACSDebitDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationBACSDebitDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationBACSDebitDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationBACSDebit struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                  `json:"available"`
	DisplayPreference *PaymentMethodConfigurationBACSDebitDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationBancontactDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationBancontactDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationBancontactDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationBancontact struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                   `json:"available"`
	DisplayPreference *PaymentMethodConfigurationBancontactDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationBillieDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationBillieDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationBillieDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationBillie struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                               `json:"available"`
	DisplayPreference *PaymentMethodConfigurationBillieDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationBLIKDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationBLIKDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationBLIKDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationBLIK struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                             `json:"available"`
	DisplayPreference *PaymentMethodConfigurationBLIKDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationBoletoDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationBoletoDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationBoletoDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationBoleto struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                               `json:"available"`
	DisplayPreference *PaymentMethodConfigurationBoletoDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationCardDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationCardDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationCardDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationCard struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                             `json:"available"`
	DisplayPreference *PaymentMethodConfigurationCardDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationCartesBancairesDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationCartesBancairesDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationCartesBancairesDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationCartesBancaires struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                        `json:"available"`
	DisplayPreference *PaymentMethodConfigurationCartesBancairesDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationCashAppDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationCashAppDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationCashAppDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationCashApp struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                `json:"available"`
	DisplayPreference *PaymentMethodConfigurationCashAppDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationCustomerBalanceDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationCustomerBalanceDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationCustomerBalanceDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationCustomerBalance struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                        `json:"available"`
	DisplayPreference *PaymentMethodConfigurationCustomerBalanceDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationEPSDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationEPSDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationEPSDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationEPS struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                            `json:"available"`
	DisplayPreference *PaymentMethodConfigurationEPSDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationFPXDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationFPXDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationFPXDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationFPX struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                            `json:"available"`
	DisplayPreference *PaymentMethodConfigurationFPXDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationGiropayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationGiropayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationGiropayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationGiropay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                `json:"available"`
	DisplayPreference *PaymentMethodConfigurationGiropayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationGooglePayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationGooglePayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationGooglePayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationGooglePay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                  `json:"available"`
	DisplayPreference *PaymentMethodConfigurationGooglePayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationGrabpayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationGrabpayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationGrabpayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationGrabpay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                `json:"available"`
	DisplayPreference *PaymentMethodConfigurationGrabpayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationIDEALDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationIDEALDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationIDEALDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationIDEAL struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                              `json:"available"`
	DisplayPreference *PaymentMethodConfigurationIDEALDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationJCBDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationJCBDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationJCBDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationJCB struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                            `json:"available"`
	DisplayPreference *PaymentMethodConfigurationJCBDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationKakaoPayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationKakaoPayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationKakaoPayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationKakaoPay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                 `json:"available"`
	DisplayPreference *PaymentMethodConfigurationKakaoPayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationKlarnaDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationKlarnaDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationKlarnaDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationKlarna struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                               `json:"available"`
	DisplayPreference *PaymentMethodConfigurationKlarnaDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationKonbiniDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationKonbiniDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationKonbiniDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationKonbini struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                `json:"available"`
	DisplayPreference *PaymentMethodConfigurationKonbiniDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationKrCardDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationKrCardDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationKrCardDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationKrCard struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                               `json:"available"`
	DisplayPreference *PaymentMethodConfigurationKrCardDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationLinkDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationLinkDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationLinkDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationLink struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                             `json:"available"`
	DisplayPreference *PaymentMethodConfigurationLinkDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationMobilepayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationMobilepayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationMobilepayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationMobilepay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                  `json:"available"`
	DisplayPreference *PaymentMethodConfigurationMobilepayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationMultibancoDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationMultibancoDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationMultibancoDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationMultibanco struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                   `json:"available"`
	DisplayPreference *PaymentMethodConfigurationMultibancoDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationNaverPayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationNaverPayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationNaverPayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationNaverPay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                 `json:"available"`
	DisplayPreference *PaymentMethodConfigurationNaverPayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationNzBankAccountDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationNzBankAccountDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationNzBankAccountDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationNzBankAccount struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                      `json:"available"`
	DisplayPreference *PaymentMethodConfigurationNzBankAccountDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationOXXODisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationOXXODisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationOXXODisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationOXXO struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                             `json:"available"`
	DisplayPreference *PaymentMethodConfigurationOXXODisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationP24DisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationP24DisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationP24DisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationP24 struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                            `json:"available"`
	DisplayPreference *PaymentMethodConfigurationP24DisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationPayByBankDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationPayByBankDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationPayByBankDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationPayByBank struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                  `json:"available"`
	DisplayPreference *PaymentMethodConfigurationPayByBankDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationPaycoDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationPaycoDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationPaycoDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationPayco struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                              `json:"available"`
	DisplayPreference *PaymentMethodConfigurationPaycoDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationPayNowDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationPayNowDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationPayNowDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationPayNow struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                               `json:"available"`
	DisplayPreference *PaymentMethodConfigurationPayNowDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationPaypalDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationPaypalDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationPaypalDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationPaypal struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                               `json:"available"`
	DisplayPreference *PaymentMethodConfigurationPaypalDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationPixDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationPixDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationPixDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationPix struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                            `json:"available"`
	DisplayPreference *PaymentMethodConfigurationPixDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationPromptPayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationPromptPayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationPromptPayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationPromptPay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                  `json:"available"`
	DisplayPreference *PaymentMethodConfigurationPromptPayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationRevolutPayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationRevolutPayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationRevolutPayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationRevolutPay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                   `json:"available"`
	DisplayPreference *PaymentMethodConfigurationRevolutPayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationSamsungPayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationSamsungPayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationSamsungPayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationSamsungPay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                   `json:"available"`
	DisplayPreference *PaymentMethodConfigurationSamsungPayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationSatispayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationSatispayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationSatispayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationSatispay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                 `json:"available"`
	DisplayPreference *PaymentMethodConfigurationSatispayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationSEPADebitDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationSEPADebitDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationSEPADebitDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationSEPADebit struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                  `json:"available"`
	DisplayPreference *PaymentMethodConfigurationSEPADebitDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationSofortDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationSofortDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationSofortDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationSofort struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                               `json:"available"`
	DisplayPreference *PaymentMethodConfigurationSofortDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationSwishDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationSwishDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationSwishDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationSwish struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                              `json:"available"`
	DisplayPreference *PaymentMethodConfigurationSwishDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationTWINTDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationTWINTDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationTWINTDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationTWINT struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                              `json:"available"`
	DisplayPreference *PaymentMethodConfigurationTWINTDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationUSBankAccountDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationUSBankAccountDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationUSBankAccountDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationUSBankAccount struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                      `json:"available"`
	DisplayPreference *PaymentMethodConfigurationUSBankAccountDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationWeChatPayDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationWeChatPayDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationWeChatPayDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationWeChatPay struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                                  `json:"available"`
	DisplayPreference *PaymentMethodConfigurationWeChatPayDisplayPreference `json:"display_preference"`
}
type PaymentMethodConfigurationZipDisplayPreference struct {
	// For child configs, whether or not the account's preference will be observed. If `false`, the parent configuration's default is used.
	Overridable bool `json:"overridable"`
	// The account's display preference.
	Preference PaymentMethodConfigurationZipDisplayPreferencePreference `json:"preference"`
	// The effective display preference value.
	Value PaymentMethodConfigurationZipDisplayPreferenceValue `json:"value"`
}
type PaymentMethodConfigurationZip struct {
	// Whether this payment method may be offered at checkout. True if `display_preference` is `on` and the payment method's capability is active.
	Available         bool                                            `json:"available"`
	DisplayPreference *PaymentMethodConfigurationZipDisplayPreference `json:"display_preference"`
}

// PaymentMethodConfigurations control which payment methods are displayed to your customers when you don't explicitly specify payment method types. You can have multiple configurations with different sets of payment methods for different scenarios.
//
// There are two types of PaymentMethodConfigurations. Which is used depends on the [charge type](https://stripe.com/docs/connect/charges):
//
// **Direct** configurations apply to payments created on your account, including Connect destination charges, Connect separate charges and transfers, and payments not involving Connect.
//
// **Child** configurations apply to payments created on your connected accounts using direct charges, and charges with the on_behalf_of parameter.
//
// Child configurations have a `parent` that sets default values and controls which settings connected accounts may override. You can specify a parent ID at payment time, and Stripe will automatically resolve the connected account's associated child configuration. Parent configurations are [managed in the dashboard](https://dashboard.stripe.com/settings/payment_methods/connected_accounts) and are not available in this API.
//
// Related guides:
// - [Payment Method Configurations API](https://stripe.com/docs/connect/payment-method-configurations)
// - [Multiple configurations on dynamic payment methods](https://stripe.com/docs/payments/multiple-payment-method-configs)
// - [Multiple configurations for your Connect accounts](https://stripe.com/docs/connect/multiple-payment-method-configurations)
type PaymentMethodConfiguration struct {
	APIResource
	ACSSDebit *PaymentMethodConfigurationACSSDebit `json:"acss_debit"`
	// Whether the configuration can be used for new payments.
	Active           bool                                        `json:"active"`
	Affirm           *PaymentMethodConfigurationAffirm           `json:"affirm"`
	AfterpayClearpay *PaymentMethodConfigurationAfterpayClearpay `json:"afterpay_clearpay"`
	Alipay           *PaymentMethodConfigurationAlipay           `json:"alipay"`
	Alma             *PaymentMethodConfigurationAlma             `json:"alma"`
	AmazonPay        *PaymentMethodConfigurationAmazonPay        `json:"amazon_pay"`
	ApplePay         *PaymentMethodConfigurationApplePay         `json:"apple_pay"`
	// For child configs, the Connect application associated with the configuration.
	Application     string                                     `json:"application"`
	AUBECSDebit     *PaymentMethodConfigurationAUBECSDebit     `json:"au_becs_debit"`
	BACSDebit       *PaymentMethodConfigurationBACSDebit       `json:"bacs_debit"`
	Bancontact      *PaymentMethodConfigurationBancontact      `json:"bancontact"`
	Billie          *PaymentMethodConfigurationBillie          `json:"billie"`
	BLIK            *PaymentMethodConfigurationBLIK            `json:"blik"`
	Boleto          *PaymentMethodConfigurationBoleto          `json:"boleto"`
	Card            *PaymentMethodConfigurationCard            `json:"card"`
	CartesBancaires *PaymentMethodConfigurationCartesBancaires `json:"cartes_bancaires"`
	CashApp         *PaymentMethodConfigurationCashApp         `json:"cashapp"`
	CustomerBalance *PaymentMethodConfigurationCustomerBalance `json:"customer_balance"`
	EPS             *PaymentMethodConfigurationEPS             `json:"eps"`
	FPX             *PaymentMethodConfigurationFPX             `json:"fpx"`
	Giropay         *PaymentMethodConfigurationGiropay         `json:"giropay"`
	GooglePay       *PaymentMethodConfigurationGooglePay       `json:"google_pay"`
	Grabpay         *PaymentMethodConfigurationGrabpay         `json:"grabpay"`
	// Unique identifier for the object.
	ID    string                           `json:"id"`
	IDEAL *PaymentMethodConfigurationIDEAL `json:"ideal"`
	// The default configuration is used whenever a payment method configuration is not specified.
	IsDefault bool                                `json:"is_default"`
	JCB       *PaymentMethodConfigurationJCB      `json:"jcb"`
	KakaoPay  *PaymentMethodConfigurationKakaoPay `json:"kakao_pay"`
	Klarna    *PaymentMethodConfigurationKlarna   `json:"klarna"`
	Konbini   *PaymentMethodConfigurationKonbini  `json:"konbini"`
	KrCard    *PaymentMethodConfigurationKrCard   `json:"kr_card"`
	Link      *PaymentMethodConfigurationLink     `json:"link"`
	// Has the value `true` if the object exists in live mode or the value `false` if the object exists in test mode.
	Livemode   bool                                  `json:"livemode"`
	Mobilepay  *PaymentMethodConfigurationMobilepay  `json:"mobilepay"`
	Multibanco *PaymentMethodConfigurationMultibanco `json:"multibanco"`
	// The configuration's name.
	Name          string                                   `json:"name"`
	NaverPay      *PaymentMethodConfigurationNaverPay      `json:"naver_pay"`
	NzBankAccount *PaymentMethodConfigurationNzBankAccount `json:"nz_bank_account"`
	// String representing the object's type. Objects of the same type share the same value.
	Object string                          `json:"object"`
	OXXO   *PaymentMethodConfigurationOXXO `json:"oxxo"`
	P24    *PaymentMethodConfigurationP24  `json:"p24"`
	// For child configs, the configuration's parent configuration.
	Parent        string                                   `json:"parent"`
	PayByBank     *PaymentMethodConfigurationPayByBank     `json:"pay_by_bank"`
	Payco         *PaymentMethodConfigurationPayco         `json:"payco"`
	PayNow        *PaymentMethodConfigurationPayNow        `json:"paynow"`
	Paypal        *PaymentMethodConfigurationPaypal        `json:"paypal"`
	Pix           *PaymentMethodConfigurationPix           `json:"pix"`
	PromptPay     *PaymentMethodConfigurationPromptPay     `json:"promptpay"`
	RevolutPay    *PaymentMethodConfigurationRevolutPay    `json:"revolut_pay"`
	SamsungPay    *PaymentMethodConfigurationSamsungPay    `json:"samsung_pay"`
	Satispay      *PaymentMethodConfigurationSatispay      `json:"satispay"`
	SEPADebit     *PaymentMethodConfigurationSEPADebit     `json:"sepa_debit"`
	Sofort        *PaymentMethodConfigurationSofort        `json:"sofort"`
	Swish         *PaymentMethodConfigurationSwish         `json:"swish"`
	TWINT         *PaymentMethodConfigurationTWINT         `json:"twint"`
	USBankAccount *PaymentMethodConfigurationUSBankAccount `json:"us_bank_account"`
	WeChatPay     *PaymentMethodConfigurationWeChatPay     `json:"wechat_pay"`
	Zip           *PaymentMethodConfigurationZip           `json:"zip"`
}

// PaymentMethodConfigurationList is a list of PaymentMethodConfigurations as retrieved from a list endpoint.
type PaymentMethodConfigurationList struct {
	APIResource
	ListMeta
	Data []*PaymentMethodConfiguration `json:"data"`
}
