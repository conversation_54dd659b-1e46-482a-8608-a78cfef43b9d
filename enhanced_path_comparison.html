<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版路径长度比较算法可视化</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 25px;
            font-size: 14px;
        }
        
        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            position: relative;
        }
        
        #gridCanvas {
            border: 3px solid #333;
            background: #fafafa;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 25px 0;
            flex-wrap: wrap;
        }
        
        button {
            padding: 12px 24px;
            font-size: 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            min-width: 120px;
        }
        
        .primary-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .primary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        
        .secondary-btn {
            background: linear-gradient(45deg, #ff9800, #e68900);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
        }
        
        .secondary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
        }
        
        .danger-btn {
            background: linear-gradient(45deg, #f44336, #da190b);
            color: white;
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
        }
        
        .danger-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
        }
        
        .generate-btn {
            background: linear-gradient(45deg, #9c27b0, #7b1fa2);
            color: white;
            box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
        }
        
        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(156, 39, 176, 0.4);
        }
        
        .info-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 25px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 10px;
        }
        
        .info-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .info-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .info-value {
            font-size: 20px;
            font-weight: bold;
        }
        
        .path1-color { color: #2196F3; }
        .path2-color { color: #FF5722; }
        .step-color { color: #4CAF50; }
        .base-path-color { color: #9c27b0; }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 25px;
            margin: 20px 0;
            font-size: 14px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .legend-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .legend-line {
            width: 20px;
            height: 4px;
            border-radius: 2px;
        }
        
        .start-point { background-color: #4CAF50; }
        .end-point { background-color: #f44336; }
        .path1-line { background-color: #2196F3; }
        .path2-line { background-color: #FF5722; }
        .eliminated-line { 
            background: repeating-linear-gradient(
                90deg,
                #999 0px,
                #999 4px,
                transparent 4px,
                transparent 8px
            );
        }
        .comparing-line { background-color: #FFD700; }
        .base-path-line { background-color: #9c27b0; }
        
        .algorithm-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
        
        .algorithm-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .algorithm-info p {
            margin: 5px 0;
            color: #333;
            font-size: 14px;
        }
        
        .status-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        
        .status-ready { background-color: #4CAF50; }
        .status-running { background-color: #ff9800; }
        .status-paused { background-color: #f44336; }
        .status-complete { background-color: #2196F3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>增强版路径长度比较算法可视化</h1>
        <p class="subtitle">基于基准路径的平行边消去算法演示</p>
        
        <div class="algorithm-info">
            <h3>算法说明</h3>
            <p><strong>1. 基准路径选择：</strong>选择一条路径作为基准路径（紫色显示）</p>
            <p><strong>2. 平行边消去：</strong>在另一条路径中寻找与基准路径平行的边进行消去</p>
            <p><strong>3. 长度比较：</strong>消去相同长度的平行边后，比较剩余路径长度</p>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-dot start-point"></div>
                <span>起点</span>
            </div>
            <div class="legend-item">
                <div class="legend-dot end-point"></div>
                <span>终点</span>
            </div>
            <div class="legend-item">
                <div class="legend-line base-path-line"></div>
                <span>基准路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-line path2-line"></div>
                <span>比较路径</span>
            </div>
            <div class="legend-item">
                <div class="legend-line comparing-line"></div>
                <span>正在比较</span>
            </div>
            <div class="legend-item">
                <div class="legend-line eliminated-line"></div>
                <span>已消去边</span>
            </div>
        </div>
        
        <div class="canvas-container">
            <canvas id="gridCanvas" width="800" height="600"></canvas>
            <div class="status-indicator status-ready" id="statusIndicator">就绪</div>
        </div>
        
        <div class="controls">
            <button class="generate-btn" onclick="generateRandomPaths()">随机生成路径</button>
            <button class="primary-btn" onclick="startAnimation()">开始动画</button>
            <button class="secondary-btn" onclick="pauseAnimation()">暂停</button>
            <button class="danger-btn" onclick="resetAnimation()">重置</button>
        </div>
        
        <div class="info-panel">
            <div class="info-item">
                <div class="info-label">当前步骤</div>
                <div class="info-value step-color" id="currentStep">0</div>
            </div>
            <div class="info-item">
                <div class="info-label">基准路径剩余长度</div>
                <div class="info-value base-path-color" id="basePathRemaining">0</div>
            </div>
            <div class="info-item">
                <div class="info-label">比较路径剩余长度</div>
                <div class="info-value path2-color" id="comparePathRemaining">0</div>
            </div>
            <div class="info-item">
                <div class="info-label">比较结果</div>
                <div class="info-value" id="comparisonResult">-</div>
            </div>
        </div>
    </div>

    <script>
        // Canvas and context
        const canvas = document.getElementById('gridCanvas');
        const ctx = canvas.getContext('2d');

        // Grid configuration - irregular spacing
        const gridWidth = 800;
        const gridHeight = 600;
        const verticalLines = [0, 80, 160, 280, 400, 520, 640, 720, 800];
        const horizontalLines = [0, 90, 180, 270, 360, 450, 540, 600];

        // Animation state
        let animationRunning = false;
        let animationPaused = false;
        let currentStep = 0;
        let animationId = null;

        // Path data structures
        let basePath = [];
        let comparePath = [];
        let basePathStates = [];
        let comparePathStates = [];
        let eliminatedPairs = [];

        // Grid intersection points for path generation
        let gridPoints = [];

        // Initialize grid points
        function initializeGridPoints() {
            gridPoints = [];
            for (let x of verticalLines) {
                for (let y of horizontalLines) {
                    gridPoints.push({x, y});
                }
            }
        }

        // Calculate edge length
        function calculateEdgeLength(edge) {
            const dx = edge.to.x - edge.from.x;
            const dy = edge.to.y - edge.from.y;
            return Math.sqrt(dx * dx + dy * dy);
        }

        // Check if two edges are parallel
        function areEdgesParallel(edge1, edge2) {
            const dx1 = edge1.to.x - edge1.from.x;
            const dy1 = edge1.to.y - edge1.from.y;
            const dx2 = edge2.to.x - edge2.from.x;
            const dy2 = edge2.to.y - edge2.from.y;

            // Check if vectors are parallel (cross product = 0)
            const crossProduct = Math.abs(dx1 * dy2 - dy1 * dx2);
            return crossProduct < 0.001; // Small tolerance for floating point
        }

        // Generate random path from start to end
        function generateRandomPath(startPoint, endPoint) {
            const path = [];
            let current = {x: startPoint.x, y: startPoint.y};
            const target = {x: endPoint.x, y: endPoint.y};

            const maxAttempts = 1000;
            let attempts = 0;

            while ((current.x !== target.x || current.y !== target.y) && attempts < maxAttempts) {
                attempts++;

                // Find possible next moves
                const possibleMoves = [];

                // Check horizontal moves
                for (let x of verticalLines) {
                    if (x !== current.x && horizontalLines.includes(current.y)) {
                        possibleMoves.push({x: x, y: current.y});
                    }
                }

                // Check vertical moves
                for (let y of horizontalLines) {
                    if (y !== current.y && verticalLines.includes(current.x)) {
                        possibleMoves.push({x: current.x, y: y});
                    }
                }

                // Filter moves that get us closer to target
                const goodMoves = possibleMoves.filter(move => {
                    const currentDistance = Math.abs(current.x - target.x) + Math.abs(current.y - target.y);
                    const newDistance = Math.abs(move.x - target.x) + Math.abs(move.y - target.y);
                    return newDistance <= currentDistance;
                });

                if (goodMoves.length === 0) {
                    // If no good moves, take any available move
                    if (possibleMoves.length === 0) break;
                    const randomMove = possibleMoves[Math.floor(Math.random() * possibleMoves.length)];
                    path.push({
                        from: {x: current.x, y: current.y},
                        to: {x: randomMove.x, y: randomMove.y},
                        length: calculateEdgeLength({from: current, to: randomMove})
                    });
                    current = randomMove;
                } else {
                    // Choose randomly from good moves
                    const randomMove = goodMoves[Math.floor(Math.random() * goodMoves.length)];
                    path.push({
                        from: {x: current.x, y: current.y},
                        to: {x: randomMove.x, y: randomMove.y},
                        length: calculateEdgeLength({from: current, to: randomMove})
                    });
                    current = randomMove;
                }
            }

            return path;
        }

        // Generate two random paths
        function generateRandomPaths() {
            const startPoint = {x: 0, y: 600};
            const endPoint = {x: 800, y: 0};

            basePath = generateRandomPath(startPoint, endPoint);
            comparePath = generateRandomPath(startPoint, endPoint);

            // Ensure paths are different
            let attempts = 0;
            while (JSON.stringify(basePath) === JSON.stringify(comparePath) && attempts < 10) {
                comparePath = generateRandomPath(startPoint, endPoint);
                attempts++;
            }

            resetAnimation();
            updateStatusIndicator('ready', '路径已生成');
        }

        // Initialize with default paths
        function initializeDefaultPaths() {
            basePath = [
                {from: {x: 0, y: 600}, to: {x: 160, y: 600}, length: 160},
                {from: {x: 160, y: 600}, to: {x: 160, y: 450}, length: 150},
                {from: {x: 160, y: 450}, to: {x: 280, y: 450}, length: 120},
                {from: {x: 280, y: 450}, to: {x: 280, y: 270}, length: 180},
                {from: {x: 280, y: 270}, to: {x: 520, y: 270}, length: 240},
                {from: {x: 520, y: 270}, to: {x: 520, y: 90}, length: 180},
                {from: {x: 520, y: 90}, to: {x: 800, y: 90}, length: 280},
                {from: {x: 800, y: 90}, to: {x: 800, y: 0}, length: 90}
            ];

            comparePath = [
                {from: {x: 0, y: 600}, to: {x: 0, y: 540}, length: 60},
                {from: {x: 0, y: 540}, to: {x: 80, y: 540}, length: 80},
                {from: {x: 80, y: 540}, to: {x: 80, y: 360}, length: 180},
                {from: {x: 80, y: 360}, to: {x: 280, y: 360}, length: 200},
                {from: {x: 280, y: 360}, to: {x: 280, y: 180}, length: 180},
                {from: {x: 280, y: 180}, to: {x: 400, y: 180}, length: 120},
                {from: {x: 400, y: 180}, to: {x: 400, y: 90}, length: 90},
                {from: {x: 400, y: 90}, to: {x: 640, y: 90}, length: 240},
                {from: {x: 640, y: 90}, to: {x: 640, y: 0}, length: 90},
                {from: {x: 640, y: 0}, to: {x: 800, y: 0}, length: 160}
            ];
        }

        // Update status indicator
        function updateStatusIndicator(status, text) {
            const indicator = document.getElementById('statusIndicator');
            indicator.className = `status-indicator status-${status}`;
            indicator.textContent = text;
        }

        // Initialize the visualization
        function init() {
            initializeGridPoints();
            initializeDefaultPaths();
            resetAnimation();
            updateStatusIndicator('ready', '就绪');
        }

        // Draw the irregular grid
        function drawGrid() {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;

            // Draw vertical lines
            verticalLines.forEach(x => {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, gridHeight);
                ctx.stroke();
            });

            // Draw horizontal lines
            horizontalLines.forEach(y => {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(gridWidth, y);
                ctx.stroke();
            });
        }

        // Draw start and end points
        function drawPoints() {
            // Start point (bottom-left)
            ctx.fillStyle = '#4CAF50';
            ctx.beginPath();
            ctx.arc(0, 600, 10, 0, 2 * Math.PI);
            ctx.fill();
            ctx.strokeStyle = '#2E7D32';
            ctx.lineWidth = 2;
            ctx.stroke();

            // End point (top-right)
            ctx.fillStyle = '#f44336';
            ctx.beginPath();
            ctx.arc(800, 0, 10, 0, 2 * Math.PI);
            ctx.fill();
            ctx.strokeStyle = '#C62828';
            ctx.lineWidth = 2;
            ctx.stroke();
        }

        // Draw both paths with current states
        function drawPaths() {
            // Draw base path (purple)
            basePath.forEach((edge, index) => {
                drawEdge(edge, getEdgeColor(basePathStates[index], '#9c27b0'), basePathStates[index], 4);
            });

            // Draw compare path (orange)
            comparePath.forEach((edge, index) => {
                drawEdge(edge, getEdgeColor(comparePathStates[index], '#FF5722'), comparePathStates[index], 3);
            });
        }

        // Draw a single edge
        function drawEdge(edge, color, state, lineWidth = 3) {
            ctx.strokeStyle = color;
            ctx.lineWidth = state === 'comparing' ? lineWidth + 2 : lineWidth;

            if (state === 'eliminated') {
                ctx.setLineDash([8, 8]);
                ctx.globalAlpha = 0.5;
            } else {
                ctx.setLineDash([]);
                ctx.globalAlpha = 1.0;
            }

            ctx.beginPath();
            ctx.moveTo(edge.from.x, edge.from.y);
            ctx.lineTo(edge.to.x, edge.to.y);
            ctx.stroke();

            // Add glow effect for comparing edges
            if (state === 'comparing') {
                ctx.shadowColor = color;
                ctx.shadowBlur = 10;
                ctx.stroke();
                ctx.shadowBlur = 0;
            }

            // Draw length label for comparing edges
            if (state === 'comparing') {
                drawEdgeLength(edge, color);
            }

            ctx.globalAlpha = 1.0;
        }

        // Draw edge length label
        function drawEdgeLength(edge, color) {
            const midX = (edge.from.x + edge.to.x) / 2;
            const midY = (edge.from.y + edge.to.y) / 2;

            ctx.fillStyle = color;
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // Draw background for text
            const text = Math.round(edge.length).toString();
            const metrics = ctx.measureText(text);
            const padding = 4;

            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(midX - metrics.width/2 - padding, midY - 8, metrics.width + 2*padding, 16);

            ctx.fillStyle = color;
            ctx.fillText(text, midX, midY);
        }

        // Get edge color based on state
        function getEdgeColor(state, defaultColor) {
            switch (state) {
                case 'eliminated': return '#999';
                case 'comparing': return '#FFD700';
                default: return defaultColor;
            }
        }

        // Calculate total remaining length for a path
        function calculateRemainingLength(path, states) {
            return path.reduce((total, edge, index) => {
                return states[index] !== 'eliminated' ? total + edge.length : total;
            }, 0);
        }

        // Update information panel
        function updateInfo() {
            document.getElementById('currentStep').textContent = currentStep;

            const baseRemaining = Math.round(calculateRemainingLength(basePath, basePathStates));
            const compareRemaining = Math.round(calculateRemainingLength(comparePath, comparePathStates));

            document.getElementById('basePathRemaining').textContent = baseRemaining;
            document.getElementById('comparePathRemaining').textContent = compareRemaining;

            // Update comparison result
            let result = '-';
            if (currentStep > 0 && !animationRunning) {
                if (baseRemaining < compareRemaining) {
                    result = '基准路径更短';
                } else if (compareRemaining < baseRemaining) {
                    result = '比较路径更短';
                } else {
                    result = '路径长度相等';
                }
            }
            document.getElementById('comparisonResult').textContent = result;
        }

        // Find parallel edges for elimination
        function findParallelEdges() {
            const pairs = [];

            for (let i = 0; i < basePath.length; i++) {
                if (basePathStates[i] === 'eliminated') continue;

                for (let j = 0; j < comparePath.length; j++) {
                    if (comparePathStates[j] === 'eliminated') continue;

                    const baseEdge = basePath[i];
                    const compareEdge = comparePath[j];

                    if (areEdgesParallel(baseEdge, compareEdge)) {
                        const lengthDiff = Math.abs(baseEdge.length - compareEdge.length);
                        pairs.push({
                            baseIndex: i,
                            compareIndex: j,
                            lengthDiff: lengthDiff,
                            minLength: Math.min(baseEdge.length, compareEdge.length)
                        });
                    }
                }
            }

            // Sort by smallest length difference first, then by larger minimum length
            pairs.sort((a, b) => {
                if (Math.abs(a.lengthDiff - b.lengthDiff) < 0.001) {
                    return b.minLength - a.minLength;
                }
                return a.lengthDiff - b.lengthDiff;
            });

            return pairs;
        }

        // Animation functions
        function startAnimation() {
            if (animationPaused) {
                animationPaused = false;
                animationRunning = true;
                updateStatusIndicator('running', '运行中');
                continueAnimation();
            } else if (!animationRunning) {
                resetAnimation();
                animationRunning = true;
                animationPaused = false;
                updateStatusIndicator('running', '运行中');
                continueAnimation();
            }
        }

        function pauseAnimation() {
            animationPaused = true;
            animationRunning = false;
            updateStatusIndicator('paused', '已暂停');
            if (animationId) {
                clearTimeout(animationId);
            }
        }

        function resetAnimation() {
            animationRunning = false;
            animationPaused = false;
            currentStep = 0;
            basePathStates = basePath.map(() => 'active');
            comparePathStates = comparePath.map(() => 'active');
            eliminatedPairs = [];

            if (animationId) {
                clearTimeout(animationId);
            }

            ctx.clearRect(0, 0, gridWidth, gridHeight);
            drawGrid();
            drawPaths();
            drawPoints();
            updateInfo();
            updateStatusIndicator('ready', '就绪');
        }

        // Continue animation with parallel edge elimination
        function continueAnimation() {
            if (!animationRunning || animationPaused) return;

            const parallelPairs = findParallelEdges();

            if (parallelPairs.length > 0 && currentStep < parallelPairs.length) {
                const pair = parallelPairs[currentStep];

                // Show comparing state
                basePathStates[pair.baseIndex] = 'comparing';
                comparePathStates[pair.compareIndex] = 'comparing';

                ctx.clearRect(0, 0, gridWidth, gridHeight);
                drawGrid();
                drawPaths();
                drawPoints();
                updateInfo();

                // Wait for comparison visualization
                animationId = setTimeout(() => {
                    // Eliminate the parallel edges
                    basePathStates[pair.baseIndex] = 'eliminated';
                    comparePathStates[pair.compareIndex] = 'eliminated';
                    eliminatedPairs.push(pair);

                    currentStep++;

                    ctx.clearRect(0, 0, gridWidth, gridHeight);
                    drawGrid();
                    drawPaths();
                    drawPoints();
                    updateInfo();

                    // Continue to next step
                    animationId = setTimeout(() => {
                        continueAnimation();
                    }, 1000);
                }, 1500);
            } else {
                // Animation complete
                animationRunning = false;
                updateStatusIndicator('complete', '完成');
                updateInfo();
            }
        }

        // Initialize when page loads
        window.onload = function() {
            init();
        };
    </script>
</body>
</html>
