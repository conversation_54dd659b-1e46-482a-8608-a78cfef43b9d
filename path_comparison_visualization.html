<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路径长度比较算法可视化</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f0f0f0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        
        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        
        #gridCanvas {
            border: 2px solid #333;
            background: white;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }
        
        button {
            padding: 10px 20px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .start-btn {
            background-color: #4CAF50;
            color: white;
        }
        
        .start-btn:hover {
            background-color: #45a049;
        }
        
        .pause-btn {
            background-color: #ff9800;
            color: white;
        }
        
        .pause-btn:hover {
            background-color: #e68900;
        }
        
        .reset-btn {
            background-color: #f44336;
            color: white;
        }
        
        .reset-btn:hover {
            background-color: #da190b;
        }
        
        .info-panel {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        
        .info-item {
            text-align: center;
        }
        
        .info-label {
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-size: 18px;
            font-weight: bold;
        }
        
        .path1-color { color: #2196F3; }
        .path2-color { color: #FF5722; }
        .step-color { color: #4CAF50; }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 15px 0;
            font-size: 14px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .legend-line {
            width: 20px;
            height: 3px;
        }
        
        .start-point { background-color: #4CAF50; }
        .end-point { background-color: #f44336; }
        .path1-line { background-color: #2196F3; }
        .path2-line { background-color: #FF5722; }
        .eliminated-line { background-color: #999; }
    </style>
</head>
<body>
    <div class="container">
        <h1>路径长度比较算法可视化演示</h1>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-dot start-point"></div>
                <span>起点</span>
            </div>
            <div class="legend-item">
                <div class="legend-dot end-point"></div>
                <span>终点</span>
            </div>
            <div class="legend-item">
                <div class="legend-line path1-line"></div>
                <span>路径1</span>
            </div>
            <div class="legend-item">
                <div class="legend-line path2-line"></div>
                <span>路径2</span>
            </div>
            <div class="legend-item">
                <div class="legend-line eliminated-line"></div>
                <span>已消去边</span>
            </div>
        </div>
        
        <div class="canvas-container">
            <canvas id="gridCanvas" width="800" height="600"></canvas>
        </div>
        
        <div class="controls">
            <button class="start-btn" onclick="startAnimation()">开始动画</button>
            <button class="pause-btn" onclick="pauseAnimation()">暂停</button>
            <button class="reset-btn" onclick="resetAnimation()">重置</button>
        </div>
        
        <div class="info-panel">
            <div class="info-item">
                <div class="info-label">当前步骤</div>
                <div class="info-value step-color" id="currentStep">0</div>
            </div>
            <div class="info-item">
                <div class="info-label">路径1剩余边数</div>
                <div class="info-value path1-color" id="path1Remaining">0</div>
            </div>
            <div class="info-item">
                <div class="info-label">路径2剩余边数</div>
                <div class="info-value path2-color" id="path2Remaining">0</div>
            </div>
            <div class="info-item">
                <div class="info-label">比较结果</div>
                <div class="info-value" id="comparisonResult">-</div>
            </div>
        </div>
    </div>

    <script>
        // Canvas and context
        const canvas = document.getElementById('gridCanvas');
        const ctx = canvas.getContext('2d');
        
        // Grid configuration
        const gridWidth = 800;
        const gridHeight = 600;
        const verticalLines = [0, 120, 200, 350, 480, 620, 800]; // Irregular spacing
        const horizontalLines = [0, 100, 180, 300, 420, 520, 600]; // Irregular spacing
        
        // Animation state
        let animationRunning = false;
        let animationPaused = false;
        let currentStep = 0;
        let animationId = null;
        
        // Path definitions (as sequences of edges)
        const path1 = [
            {from: {x: 0, y: 600}, to: {x: 120, y: 600}, type: 'horizontal'},
            {from: {x: 120, y: 600}, to: {x: 120, y: 520}, type: 'vertical'},
            {from: {x: 120, y: 520}, to: {x: 200, y: 520}, type: 'horizontal'},
            {from: {x: 200, y: 520}, to: {x: 200, y: 420}, type: 'vertical'},
            {from: {x: 200, y: 420}, to: {x: 350, y: 420}, type: 'horizontal'},
            {from: {x: 350, y: 420}, to: {x: 350, y: 300}, type: 'vertical'},
            {from: {x: 350, y: 300}, to: {x: 480, y: 300}, type: 'horizontal'},
            {from: {x: 480, y: 300}, to: {x: 480, y: 180}, type: 'vertical'},
            {from: {x: 480, y: 180}, to: {x: 620, y: 180}, type: 'horizontal'},
            {from: {x: 620, y: 180}, to: {x: 620, y: 100}, type: 'vertical'},
            {from: {x: 620, y: 100}, to: {x: 800, y: 100}, type: 'horizontal'},
            {from: {x: 800, y: 100}, to: {x: 800, y: 0}, type: 'vertical'}
        ];
        
        const path2 = [
            {from: {x: 0, y: 600}, to: {x: 0, y: 520}, type: 'vertical'},
            {from: {x: 0, y: 520}, to: {x: 120, y: 520}, type: 'horizontal'},
            {from: {x: 120, y: 520}, to: {x: 120, y: 420}, type: 'vertical'},
            {from: {x: 120, y: 420}, to: {x: 200, y: 420}, type: 'horizontal'},
            {from: {x: 200, y: 420}, to: {x: 200, y: 300}, type: 'vertical'},
            {from: {x: 200, y: 300}, to: {x: 350, y: 300}, type: 'horizontal'},
            {from: {x: 350, y: 300}, to: {x: 350, y: 180}, type: 'vertical'},
            {from: {x: 350, y: 180}, to: {x: 480, y: 180}, type: 'horizontal'},
            {from: {x: 480, y: 180}, to: {x: 480, y: 100}, type: 'vertical'},
            {from: {x: 480, y: 100}, to: {x: 620, y: 100}, type: 'horizontal'},
            {from: {x: 620, y: 100}, to: {x: 620, y: 0}, type: 'vertical'},
            {from: {x: 620, y: 0}, to: {x: 800, y: 0}, type: 'horizontal'}
        ];
        
        // Edge states for animation
        let path1States = path1.map(() => 'active'); // 'active', 'eliminated', 'comparing'
        let path2States = path2.map(() => 'active');

        // Initialize the visualization
        function init() {
            drawGrid();
            drawPaths();
            drawPoints();
            updateInfo();
        }

        // Draw the irregular grid
        function drawGrid() {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;

            // Draw vertical lines
            verticalLines.forEach(x => {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, gridHeight);
                ctx.stroke();
            });

            // Draw horizontal lines
            horizontalLines.forEach(y => {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(gridWidth, y);
                ctx.stroke();
            });
        }

        // Draw start and end points
        function drawPoints() {
            // Start point (bottom-left)
            ctx.fillStyle = '#4CAF50';
            ctx.beginPath();
            ctx.arc(0, 600, 8, 0, 2 * Math.PI);
            ctx.fill();

            // End point (top-right)
            ctx.fillStyle = '#f44336';
            ctx.beginPath();
            ctx.arc(800, 0, 8, 0, 2 * Math.PI);
            ctx.fill();
        }

        // Draw both paths with current states
        function drawPaths() {
            // Draw path 1
            path1.forEach((edge, index) => {
                drawEdge(edge, getEdgeColor(path1States[index], '#2196F3'), path1States[index]);
            });

            // Draw path 2
            path2.forEach((edge, index) => {
                drawEdge(edge, getEdgeColor(path2States[index], '#FF5722'), path2States[index]);
            });
        }

        // Draw a single edge
        function drawEdge(edge, color, state) {
            ctx.strokeStyle = color;
            ctx.lineWidth = state === 'comparing' ? 4 : 3;

            if (state === 'eliminated') {
                ctx.setLineDash([5, 5]);
            } else {
                ctx.setLineDash([]);
            }

            ctx.beginPath();
            ctx.moveTo(edge.from.x, edge.from.y);
            ctx.lineTo(edge.to.x, edge.to.y);
            ctx.stroke();

            // Add arrow for comparing edges
            if (state === 'comparing') {
                drawArrow(edge, color);
            }
        }

        // Draw arrow on edge
        function drawArrow(edge, color) {
            const midX = (edge.from.x + edge.to.x) / 2;
            const midY = (edge.from.y + edge.to.y) / 2;

            ctx.fillStyle = color;
            ctx.beginPath();

            if (edge.type === 'horizontal') {
                const direction = edge.to.x > edge.from.x ? 1 : -1;
                ctx.moveTo(midX, midY);
                ctx.lineTo(midX - 8 * direction, midY - 4);
                ctx.lineTo(midX - 8 * direction, midY + 4);
            } else {
                const direction = edge.to.y > edge.from.y ? 1 : -1;
                ctx.moveTo(midX, midY);
                ctx.lineTo(midX - 4, midY - 8 * direction);
                ctx.lineTo(midX + 4, midY - 8 * direction);
            }

            ctx.closePath();
            ctx.fill();
        }

        // Get edge color based on state
        function getEdgeColor(state, defaultColor) {
            switch (state) {
                case 'eliminated': return '#999';
                case 'comparing': return '#FFD700';
                default: return defaultColor;
            }
        }

        // Update information panel
        function updateInfo() {
            document.getElementById('currentStep').textContent = currentStep;
            document.getElementById('path1Remaining').textContent =
                path1States.filter(state => state === 'active').length;
            document.getElementById('path2Remaining').textContent =
                path2States.filter(state => state === 'active').length;

            // Update comparison result
            const path1Remaining = path1States.filter(state => state === 'active').length;
            const path2Remaining = path2States.filter(state => state === 'active').length;

            let result = '-';
            if (currentStep > 0 && !animationRunning) {
                if (path1Remaining < path2Remaining) {
                    result = '路径1更短';
                } else if (path2Remaining < path1Remaining) {
                    result = '路径2更短';
                } else {
                    result = '路径长度相等';
                }
            }
            document.getElementById('comparisonResult').textContent = result;
        }

        // Animation functions
        function startAnimation() {
            if (animationPaused) {
                animationPaused = false;
                animationRunning = true;
                continueAnimation();
            } else if (!animationRunning) {
                resetAnimation();
                animationRunning = true;
                animationPaused = false;
                continueAnimation();
            }
        }

        function pauseAnimation() {
            animationPaused = true;
            animationRunning = false;
            if (animationId) {
                clearTimeout(animationId);
            }
        }

        function resetAnimation() {
            animationRunning = false;
            animationPaused = false;
            currentStep = 0;
            path1States = path1.map(() => 'active');
            path2States = path2.map(() => 'active');

            if (animationId) {
                clearTimeout(animationId);
            }

            ctx.clearRect(0, 0, gridWidth, gridHeight);
            init();
        }

        // Continue animation with step-by-step comparison
        function continueAnimation() {
            if (!animationRunning || animationPaused) return;

            const maxSteps = Math.max(path1.length, path2.length);

            if (currentStep < maxSteps) {
                // Show comparing state
                if (currentStep < path1.length) {
                    path1States[currentStep] = 'comparing';
                }
                if (currentStep < path2.length) {
                    path2States[currentStep] = 'comparing';
                }

                ctx.clearRect(0, 0, gridWidth, gridHeight);
                drawGrid();
                drawPaths();
                drawPoints();
                updateInfo();

                // Wait for comparison visualization
                animationId = setTimeout(() => {
                    // Check if edges can be eliminated
                    let canEliminate = false;

                    if (currentStep < path1.length && currentStep < path2.length) {
                        const edge1 = path1[currentStep];
                        const edge2 = path2[currentStep];

                        // Check if edges are equivalent (same type and similar direction)
                        if (edge1.type === edge2.type) {
                            canEliminate = true;
                        }
                    }

                    // Update states based on comparison
                    if (canEliminate) {
                        if (currentStep < path1.length) {
                            path1States[currentStep] = 'eliminated';
                        }
                        if (currentStep < path2.length) {
                            path2States[currentStep] = 'eliminated';
                        }
                    } else {
                        if (currentStep < path1.length) {
                            path1States[currentStep] = 'active';
                        }
                        if (currentStep < path2.length) {
                            path2States[currentStep] = 'active';
                        }
                    }

                    currentStep++;

                    ctx.clearRect(0, 0, gridWidth, gridHeight);
                    drawGrid();
                    drawPaths();
                    drawPoints();
                    updateInfo();

                    // Continue to next step
                    animationId = setTimeout(() => {
                        continueAnimation();
                    }, 800);
                }, 1200);
            } else {
                // Animation complete
                animationRunning = false;
                updateInfo();
            }
        }

        // Initialize when page loads
        window.onload = function() {
            init();
        };
    </script>
</body>
</html>
