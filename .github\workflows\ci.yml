name: CI

on:
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch: {}
  push:
    branches:
      - master
      - beta
      - sdk-release/**
      - feature/**
    tags:
      - v[0-9]+.[0-9]+.[0-9]+*
  pull_request:
    branches:
      - master
      - beta
      - sdk-release/**
      - feature/**

jobs:
  lint:
    name: <PERSON>t

    runs-on: "ubuntu-24.04"

    steps:
      - uses: extractions/setup-just@v2
      - uses: actions/checkout@master

      - name: Setup go
        uses: actions/setup-go@v1
        with:
          go-version: "1.19"

      - name: lint
        run: |
          just lint format-check

  govulncheck:
    name: govulncheck
    runs-on: "ubuntu-24.04"
    steps:
      - uses: actions/checkout@v4
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.24'
          check-latest: true
      - name: Install govulncheck
        run: go install golang.org/x/vuln/cmd/govulncheck@latest
      - name: Run vulnerability scan
        run: govulncheck ./...

  test:
    runs-on: "ubuntu-24.04"
    strategy:
      matrix:
        go:
          - "1.24"
          - "1.23"
          - "1.22"
          - "1.21"
          - "1.20"
          - "1.19"
          - "1.18"
    name: "Test: go v${{ matrix.go }}"
    steps:
      - uses: extractions/setup-just@v2
      - uses: actions/checkout@v2
      - name: Setup go
        uses: actions/setup-go@v1
        with:
          go-version: ${{ matrix.go }}
      - uses: stripe/openapi/actions/stripe-mock@master
      - name: Test
        run: just ci-test

  publish:
    name: Publish
    if: >-
      (github.event_name == 'workflow_dispatch' || github.event_name == 'push') &&
      startsWith(github.ref, 'refs/tags/v') &&
      endsWith(github.actor, '-stripe')
    runs-on: "ubuntu-24.04"
    steps:
      - uses: actions/checkout@v2
      - uses: stripe/openapi/actions/notify-release@master
        if: always()
        with:
          bot_token: ${{ secrets.SLACK_BOT_TOKEN }}
